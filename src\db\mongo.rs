use crate::config::MongoConfig;
use crate::models::{SolarModule, SyncState};
use anyhow::Result;
use mongodb::bson::{doc, to_document, DateTime};
use mongodb::options::UpdateOptions;
use mongodb::{Client, Database};

#[derive(Clone)]
pub struct MongoClient {
    db: Database,
}

impl MongoClient {
    pub async fn new(config: &MongoConfig) -> Result<Self> {
        let client = Client::with_uri_str(&config.uri).await?;
        let db = client.database(&config.database);
        Ok(Self { db })
    }

    pub async fn save_modules(&self, modules: Vec<SolarModule>) -> Result<()> {
        let collection = self.db.collection::<SolarModule>("NewModuleInfo");
        
        // 将数据分批处理,每批1000条
        for chunk in modules.chunks(1000) {
            for module in chunk {
                let filter = doc! { "LotNumber": &module.lot_number };
                let update = doc! { "$set": to_document(&module)? };
                let options = UpdateOptions::builder().upsert(true).build();
                
                collection.update_one(filter, update, options).await?;
            }
        }
        
        Ok(())
    }

    pub async fn get_sync_state(&self) -> Result<SyncState> {
        let collection = self.db.collection::<SyncState>("sync_state");
        match collection.find_one(None, None).await? {
            Some(state) => Ok(state),
            None => Ok(SyncState {
                last_sync_time: DateTime::from_millis(0),
                last_lot_number: None,
            }),
        }
    }

    pub async fn update_sync_state(&self, state: &SyncState) -> Result<()> {
        let collection = self.db.collection::<SyncState>("sync_state");

        collection
            .update_one(
                doc! {},
                doc! { "$set": {
                    "last_sync_time": state.last_sync_time,
                    "last_lot_number": &state.last_lot_number
                }},
                UpdateOptions::builder().upsert(true).build(),
            )
            .await?;

        Ok(())
    }

    pub async fn save_modules_with_upsert(&self, modules: Vec<SolarModule>) -> Result<()> {
        let collection = self.db.collection::<SolarModule>("NewModuleInfo");
        
        for module in modules {
            let filter = doc! { "LotNumber": &module.lot_number };
            let update = doc! { "$set": to_document(&module)? };
            let options = UpdateOptions::builder().upsert(true).build();
            
            collection.update_one(filter, update, options).await?;
        }
        
        Ok(())
    }
}
