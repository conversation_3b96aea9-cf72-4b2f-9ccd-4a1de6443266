// src/routes/admin.rs
// 管理接口，包括日志管理

use actix_web::{get, post, web, HttpResponse};
use serde::{Deserialize, Serialize};
use log::{info, error};
use crate::logging;

#[derive(Serialize)]
pub struct LogStatsResponse {
    pub total_files: u32,
    pub total_size_mb: f64,
    pub largest_file_name: String,
    pub largest_file_size_mb: f64,
    pub log_directory: String,
}

#[derive(Deserialize)]
pub struct CleanupRequest {
    pub days_to_keep: u64,
}

#[derive(Serialize)]
pub struct CleanupResponse {
    pub success: bool,
    pub message: String,
}

/// 获取日志统计信息
#[get("/api/admin/logs/stats")]
pub async fn get_log_stats() -> HttpResponse {
    let log_dir = "logs"; // 可以从配置中获取

    match logging::get_log_stats(log_dir) {
        Ok(stats) => {
            let response = LogStatsResponse {
                total_files: stats.total_files,
                total_size_mb: stats.total_size_mb(),
                largest_file_name: stats.largest_file_name.clone(),
                largest_file_size_mb: stats.largest_file_size_mb(),
                log_directory: log_dir.to_string(),
            };

            info!("日志统计查询成功: {} 个文件, {:.2}MB", stats.total_files, stats.total_size_mb());
            HttpResponse::Ok().json(response)
        }
        Err(e) => {
            error!("获取日志统计失败: {}", e);
            HttpResponse::InternalServerError().json(format!("获取日志统计失败: {}", e))
        }
    }
}

/// 清理旧日志文件
#[post("/api/admin/logs/cleanup")]
pub async fn cleanup_logs(request: web::Json<CleanupRequest>) -> HttpResponse {
    let log_dir = "logs"; // 可以从配置中获取

    if request.days_to_keep == 0 {
        return HttpResponse::BadRequest().json(CleanupResponse {
            success: false,
            message: "保留天数必须大于0".to_string(),
        });
    }

    if request.days_to_keep < 7 {
        return HttpResponse::BadRequest().json(CleanupResponse {
            success: false,
            message: "为了安全起见，保留天数不能少于7天".to_string(),
        });
    }

    match logging::cleanup_old_logs(log_dir, request.days_to_keep) {
        Ok(()) => {
            let message = format!("成功清理{}天前的旧日志文件", request.days_to_keep);
            info!("{}", message);
            HttpResponse::Ok().json(CleanupResponse {
                success: true,
                message,
            })
        }
        Err(e) => {
            let message = format!("清理日志文件失败: {}", e);
            error!("{}", message);
            HttpResponse::InternalServerError().json(CleanupResponse {
                success: false,
                message,
            })
        }
    }
}

/// 获取系统状态信息
#[get("/api/admin/status")]
pub async fn get_system_status() -> HttpResponse {
    #[derive(Serialize)]
    struct SystemStatus {
        uptime_seconds: u64,
        log_stats: LogStatsResponse,
        memory_usage_mb: f64,
    }

    // 获取系统运行时间（简化版本）
    let uptime = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();

    // 获取日志统计
    let log_dir = "logs";
    let log_stats = match logging::get_log_stats(log_dir) {
        Ok(stats) => LogStatsResponse {
            total_files: stats.total_files,
            total_size_mb: stats.total_size_mb(),
            largest_file_name: stats.largest_file_name.clone(),
            largest_file_size_mb: stats.largest_file_size_mb(),
            log_directory: log_dir.to_string(),
        },
        Err(_) => LogStatsResponse {
            total_files: 0,
            total_size_mb: 0.0,
            largest_file_name: "unknown".to_string(),
            largest_file_size_mb: 0.0,
            log_directory: log_dir.to_string(),
        },
    };

    // 获取内存使用情况（简化版本）
    let memory_usage = get_memory_usage();

    let status = SystemStatus {
        uptime_seconds: uptime,
        log_stats,
        memory_usage_mb: memory_usage,
    };

    HttpResponse::Ok().json(status)
}

/// 获取内存使用情况（简化实现）
fn get_memory_usage() -> f64 {
    // 这里可以使用系统调用获取实际内存使用情况
    // 为了简化，返回一个估算值
    0.0
}
