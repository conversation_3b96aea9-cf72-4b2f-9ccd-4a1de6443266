@echo off
echo ===== Product Data Sync Server Deployment =====

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Configuration
set SERVICE_NAME=ProductDataSync
set SERVICE_DISPLAY_NAME=Product Data Sync Service
set APP_DIR=D:\0.MES\0.ANewCode\product-data-sync
set ORACLE_DIR=D:\0.MES\0.ANewCode\instantclient_23_6
set LOG_DIR=%APP_DIR%\logs

echo 1. Setting up directories...
if not exist "%APP_DIR%" (
    echo ERROR: Application directory not found: %APP_DIR%
    pause
    exit /b 1
)

if not exist "%ORACLE_DIR%" (
    echo ERROR: Oracle client directory not found: %ORACLE_DIR%
    pause
    exit /b 1
)

if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    echo Created log directory: %LOG_DIR%
)

echo 2. Setting system environment variables...
setx OCI_LIB_DIR "%ORACLE_DIR%" /M
setx ORACLE_HOME "%ORACLE_DIR%" /M

REM Get current system PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set SYSTEM_PATH=%%b

REM Check if Oracle path is already in system PATH
echo %SYSTEM_PATH% | findstr /C:"%ORACLE_DIR%" >nul
if %errorLevel% neq 0 (
    echo Adding Oracle path to system PATH...
    setx PATH "%ORACLE_DIR%;%SYSTEM_PATH%" /M
) else (
    echo Oracle path already in system PATH
)

echo 3. Building release version...
cd /d "%APP_DIR%"
cargo build --release
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo 4. Creating service wrapper script...
(
echo @echo off
echo REM Service wrapper for Product Data Sync
echo set OCI_LIB_DIR=%ORACLE_DIR%
echo set ORACLE_HOME=%ORACLE_DIR%
echo set PATH=%ORACLE_DIR%;%%PATH%%
echo cd /d "%APP_DIR%"
echo "%APP_DIR%\target\release\product-data-sync.exe"
) > "%APP_DIR%\service_start.bat"

echo 5. Installing Windows service...
REM Stop service if it exists
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Stopping existing service...
    sc stop "%SERVICE_NAME%"
    timeout /t 5 /nobreak >nul
    sc delete "%SERVICE_NAME%"
    timeout /t 2 /nobreak >nul
)

REM Create new service
sc create "%SERVICE_NAME%" ^
    binPath= "\"%APP_DIR%\service_start.bat\"" ^
    DisplayName= "%SERVICE_DISPLAY_NAME%" ^
    start= auto ^
    type= own

if %errorLevel% neq 0 (
    echo ERROR: Failed to create service
    pause
    exit /b 1
)

echo 6. Configuring service...
REM Set service description
sc description "%SERVICE_NAME%" "Product Data Sync Service for Oracle and MongoDB integration"

REM Set service recovery options
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/5000/restart/5000/restart/5000

echo 7. Starting service...
sc start "%SERVICE_NAME%"

echo 8. Checking service status...
timeout /t 5 /nobreak >nul
sc query "%SERVICE_NAME%"

echo.
echo ===== Deployment Complete =====
echo Service Name: %SERVICE_NAME%
echo Service Status: Use 'sc query %SERVICE_NAME%' to check
echo Application Directory: %APP_DIR%
echo Log Directory: %LOG_DIR%
echo.
echo To manage the service:
echo   Start:   sc start %SERVICE_NAME%
echo   Stop:    sc stop %SERVICE_NAME%
echo   Status:  sc query %SERVICE_NAME%
echo   Remove:  sc delete %SERVICE_NAME%
echo.
echo API Endpoints will be available at:
echo   http://localhost:8080/api/modules/query-by-pallet
echo   http://localhost:8080/api/modules/query-by-lot-number
echo   http://localhost:8080/api/admin/status
echo.

pause
