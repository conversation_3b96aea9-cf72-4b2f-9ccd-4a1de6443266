[package]
name = "product-data-sync"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.28", features = ["full"] }
r2d2 = "0.8"
r2d2-oracle = "0.6"  # 修正了这里的包名，把下划线改为横线
mongodb = "2.8"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
config = "0.13"
chrono = "0.4"     # 添加 chrono 依赖，因为我们在代码中用到了
actix-web = "4.4"
log4rs = "1.2"