// src/tasks/mod.rs
use crate::{
    db::{MongoClient, OracleClient},
    models::SyncState,
};
use anyhow::Result;
use chrono::{DateTime as ChronoDateTime, NaiveDateTime, Utc};
use log::{error, info};
use std::time::Duration;
use tokio::time;

pub struct SyncTask {
    oracle: OracleClient,
    mongo: MongoClient,
    interval: Duration,
    batch_size: i32, // 添加批次大小字段
    min_sync_interval_days: u64, // 最小同步间隔天数
}

impl SyncTask {
    pub fn new(
        oracle: OracleClient,
        mongo: MongoClient,
        interval_seconds: u64,
        batch_size: i32,
        min_sync_interval_days: u64,
    ) -> Self {
        Self {
            oracle,
            mongo,
            interval: Duration::from_secs(interval_seconds),
            batch_size,
            min_sync_interval_days,
        }
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting sync task...");
        let mut interval = time::interval(self.interval);

        loop {
            interval.tick().await;
            if let Err(e) = self.run_sync().await {
                error!("Sync error: {}", e);
            }
        }
    }

    async fn run_sync(&self) -> Result<()> {
        let task_start_time = std::time::Instant::now();
        info!("Running sync task...");

        let state_start = std::time::Instant::now();
        let sync_state = self.mongo.get_sync_state().await?;
        info!("Get sync state took: {:?}", state_start.elapsed());

        // 检查是否需要跳过同步（基于时间间隔）
        if sync_state.last_sync_time.timestamp_millis() != 0 {
            let now = Utc::now();
            let last_sync_millis = sync_state.last_sync_time.timestamp_millis();
            let last_sync = ChronoDateTime::<Utc>::from_timestamp_millis(last_sync_millis)
                .unwrap_or(now);
            let duration_since_last_sync = now.signed_duration_since(last_sync);
            let days_since_last_sync = duration_since_last_sync.num_days();

            if days_since_last_sync < self.min_sync_interval_days as i64 {
                info!(
                    "跳过同步：距离上次同步仅{}天，小于配置的最小间隔{}天",
                    days_since_last_sync,
                    self.min_sync_interval_days
                );
                return Ok(());
            } else {
                info!(
                    "继续同步：距离上次同步{}天，超过配置的最小间隔{}天",
                    days_since_last_sync,
                    self.min_sync_interval_days
                );
            }
        }

        let (start_time, lot_number) = if sync_state.last_sync_time.timestamp_millis() == 0 {
            let naive_dt =
                NaiveDateTime::parse_from_str("2023-01-01 00:00:00", "%Y-%m-%d %H:%M:%S")?;
            let utc_dt = ChronoDateTime::<Utc>::from_naive_utc_and_offset(naive_dt, Utc);
            (
                mongodb::bson::DateTime::from_millis(utc_dt.timestamp_millis()),
                Some("1".to_string()),
            )
        } else {
            (sync_state.last_sync_time, sync_state.last_lot_number)
        };

        let query_start = std::time::Instant::now();
        let (modules, max_product_date) =
            self.oracle
                .query_modules(start_time, lot_number)?;
        info!(
            "Oracle query took: {:?}, retrieved {} records",
            query_start.elapsed(),
            modules.len()
        );

        if !modules.is_empty() {
            let last_module = modules.last().unwrap();
            let new_state = SyncState {
                // 使用 max_product_date 作为同步时间点
                last_sync_time: max_product_date.unwrap_or(last_module.product_date),
                last_lot_number: Some(last_module.lot_number.clone()),
            };

            let save_start = std::time::Instant::now();
            self.mongo.save_modules(modules).await?;
            info!("MongoDB save took: {:?}", save_start.elapsed());

            let update_start = std::time::Instant::now();
            self.mongo.update_sync_state(&new_state).await?;
            info!("State update took: {:?}", update_start.elapsed());
        }

        info!("Total sync task took: {:?}", task_start_time.elapsed());
        Ok(())
    }
}
