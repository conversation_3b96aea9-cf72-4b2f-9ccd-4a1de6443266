# Oracle连接问题深度分析

## 🔍 问题现状

### 服务端环境（可正常运行）
- ✅ 系统环境变量：`OCI_LIB_DIR=D:\0.MES\0.ANewCode\instantclient_23_6`
- ✅ 系统环境变量：`PATH`包含Oracle路径
- ✅ 程序可以正常连接Oracle数据库

### 本地环境（无法运行）
- ✅ 系统环境变量：`OCI_LIB_DIR=D:\0.MES\0.ANewCode\instantclient_23_6`
- ✅ 系统环境变量：`PATH`包含Oracle路径
- ❌ 程序报错：`DPI-1047: Cannot locate a 64-bit Oracle Client library`

## 🔍 可能的原因分析

### 1. PowerShell会话环境变量问题
**问题**：当前PowerShell会话没有正确加载系统环境变量
**验证**：
```powershell
# 系统级环境变量（正确）
[Environment]::GetEnvironmentVariable("OCI_LIB_DIR", [EnvironmentVariableTarget]::Machine)
# 输出：D:\0.MES\0.ANewCode\instantclient_23_6

# 当前会话环境变量（错误）
$env:OCI_LIB_DIR
# 输出：空
```

### 2. 进程继承环境变量问题
**问题**：cargo run启动的进程没有继承正确的环境变量
**原因**：PowerShell会话本身没有正确的环境变量，子进程继承了错误的环境

### 3. Oracle客户端版本兼容性
**问题**：Oracle Instant Client 23.6可能与当前的oracle crate版本不兼容
**服务端差异**：服务端可能有不同的运行环境或配置

### 4. DLL依赖问题
**问题**：可能缺少某些依赖的DLL文件
**检查**：虽然主要的Oracle DLL存在，但可能缺少其他依赖

## 💡 解决方案

### 方案1：重启PowerShell（最简单）
```powershell
# 1. 完全关闭当前PowerShell
# 2. 重新打开PowerShell
# 3. 验证环境变量
echo "OCI_LIB_DIR: $env:OCI_LIB_DIR"
$env:PATH -split ';' | Where-Object { $_ -like "*instantclient*" }
# 4. 运行程序
cd D:\0.MES\0.ANewCode\product-data-sync
cargo run
```

### 方案2：使用批处理文件启动
创建 `run_with_oracle.bat`：
```batch
@echo off
set OCI_LIB_DIR=D:\0.MES\0.ANewCode\instantclient_23_6
set PATH=D:\0.MES\0.ANewCode\instantclient_23_6;%PATH%
cd /d D:\0.MES\0.ANewCode\product-data-sync
cargo run
pause
```

### 方案3：使用PowerShell脚本启动
创建 `run_with_oracle.ps1`：
```powershell
$env:OCI_LIB_DIR = "D:\0.MES\0.ANewCode\instantclient_23_6"
$env:PATH = "D:\0.MES\0.ANewCode\instantclient_23_6;" + $env:PATH
Set-Location "D:\0.MES\0.ANewCode\product-data-sync"
cargo run
```

### 方案4：检查Oracle客户端版本兼容性
```powershell
# 检查Oracle客户端版本
Get-ChildItem "D:\0.MES\0.ANewCode\instantclient_23_6\*.dll" | ForEach-Object {
    $version = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($_.FullName)
    Write-Host "$($_.Name): $($version.FileVersion)"
}
```

### 方案5：使用Process Monitor诊断
1. 下载Process Monitor (ProcMon)
2. 运行ProcMon并设置过滤器：Process Name contains "product-data-sync"
3. 运行程序，观察文件访问失败的详细信息
4. 查看程序尝试加载哪些DLL文件以及失败原因

## 🔧 高级诊断

### 检查DLL依赖
```powershell
# 使用dumpbin检查DLL依赖（需要Visual Studio）
dumpbin /dependents "D:\0.MES\0.ANewCode\instantclient_23_6\oci.dll"

# 或使用PowerShell检查文件属性
Get-ItemProperty "D:\0.MES\0.ANewCode\instantclient_23_6\oci.dll" | Select-Object *
```

### 检查系统架构匹配
```powershell
# 检查系统架构
[Environment]::Is64BitOperatingSystem
[Environment]::Is64BitProcess

# 检查Rust编译目标
cargo --version
rustc --print target-list | Select-String "x86_64-pc-windows"
```

### 检查运行时库
```powershell
# 检查Visual C++ Redistributable
Get-WmiObject -Class Win32_Product | Where-Object { 
    $_.Name -like "*Visual C++*" -and $_.Name -like "*2019*" 
} | Select-Object Name, Version
```

## 🎯 推荐操作步骤

### 立即尝试
1. **关闭所有PowerShell窗口**
2. **重新打开PowerShell**
3. **运行验证脚本**：
   ```powershell
   .\verify_env.ps1
   ```
4. **如果环境变量正确，运行程序**：
   ```powershell
   cargo run
   ```

### 如果仍然失败
1. **使用批处理文件启动**（方案2）
2. **或者重启整个系统**（确保环境变量完全生效）
3. **检查Oracle客户端版本兼容性**

## 🔍 服务端成功的可能原因

1. **服务端使用不同的启动方式**（如Windows服务、任务计划程序）
2. **服务端环境变量在系统启动时就正确加载**
3. **服务端可能有额外的配置或依赖**
4. **服务端使用的是不同版本的Rust或编译配置**

## 📝 调试建议

如果上述方案都不能解决问题，建议：

1. **对比服务端和本地的完整环境**：
   - Rust版本
   - 编译目标
   - 系统环境变量
   - 运行时库版本

2. **使用Process Monitor监控文件访问**
3. **检查Windows事件日志**中的相关错误
4. **考虑使用Docker容器化部署**以确保环境一致性
