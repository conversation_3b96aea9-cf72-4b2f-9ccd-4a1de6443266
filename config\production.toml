# Production configuration for Product Data Sync

[oracle]
host = "*************"
port = 1521
database = "HNZJMESDG"
username = "HNZJMESDB"
password = "Sie$2023Camstar"
pool_size = 10
connection_timeout = 30

[mongodb]
uri = "mongodb://localhost:27017"
database = "product_sync"
collection = "solar_modules"
pool_size = 10
connection_timeout = 30

[sync]
interval_seconds = 300  # 5 minutes for production
batch_size = 50         # Larger batch size for production

[logging]
level = "info"
max_file_size_mb = 50   # Larger log files for production
max_files = 60          # Keep more history
log_dir = "logs"
enable_console = false  # Disable console output for service
cleanup_days = 30

[app]
bind_address = "0.0.0.0"  # Listen on all interfaces
port = 8080
workers = 4               # Number of worker threads
