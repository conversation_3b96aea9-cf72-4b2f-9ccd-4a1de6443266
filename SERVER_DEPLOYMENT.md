# 服务器部署指南

## 🚀 部署方案选择

### 方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **Windows服务** | 系统集成好，自动启动 | 配置复杂 | 生产环境推荐 |
| **NSSM** | 功能强大，日志管理好 | 需要额外工具 | 企业环境 |
| **任务计划程序** | 简单易用，自动重启 | 管理界面有限 | 中小型部署 |
| **Docker** | 环境隔离，易迁移 | 需要Docker环境 | 容器化环境 |

## 📋 部署前准备

### 1. 系统要求
- Windows Server 2016+ 或 Windows 10+
- .NET Framework 4.7.2+
- Visual C++ Redistributable 2019+
- 管理员权限

### 2. 依赖检查
```powershell
# 检查Rust环境
rustc --version
cargo --version

# 检查Oracle客户端
Test-Path "D:\0.MES\0.ANewCode\instantclient_23_6\oci.dll"

# 检查网络连接
Test-NetConnection -ComputerName 10.123.226.52 -Port 1521
```

### 3. 构建发布版本
```bash
cargo build --release
```

## 🛠️ 部署步骤

### 方案1：Windows服务部署（推荐）

```bash
# 以管理员身份运行
.\deploy_server.bat
```

**特点：**
- 自动设置系统环境变量
- 创建Windows服务
- 配置自动启动和故障恢复
- 集成系统日志

**管理命令：**
```bash
# 启动服务
sc start ProductDataSync

# 停止服务
sc stop ProductDataSync

# 查看状态
sc query ProductDataSync

# 删除服务
sc delete ProductDataSync
```

### 方案2：NSSM部署

```bash
# 1. 下载NSSM
# 访问 https://nssm.cc/download
# 解压nssm.exe到项目目录

# 2. 以管理员身份运行
.\deploy_with_nssm.bat
```

**特点：**
- 更好的服务管理
- 自动日志轮转
- 环境变量隔离
- 图形化配置界面

**管理命令：**
```bash
nssm start ProductDataSync
nssm stop ProductDataSync
nssm restart ProductDataSync
nssm edit ProductDataSync  # 图形界面配置
```

### 方案3：任务计划程序部署

```bash
# 以管理员身份运行
.\deploy_task_scheduler.bat
```

**特点：**
- 简单易用
- 自动重启机制
- 系统启动时自动运行
- 无需额外工具

### 方案4：Docker部署

```bash
# 1. 安装Docker Desktop
# 2. 运行部署脚本
.\deploy_docker.bat
```

**特点：**
- 环境隔离
- 易于迁移
- 版本管理
- 资源限制

## 🔧 配置管理

### 环境变量设置
```bash
# 系统级环境变量（推荐）
setx OCI_LIB_DIR "D:\0.MES\0.ANewCode\instantclient_23_6" /M
setx ORACLE_HOME "D:\0.MES\0.ANewCode\instantclient_23_6" /M
setx PATH "D:\0.MES\0.ANewCode\instantclient_23_6;%PATH%" /M
```

### 配置文件
- **开发环境**: `config/default.toml`
- **生产环境**: `config/production.toml`

使用环境变量指定配置：
```bash
set CONFIG_ENV=production
```

### 日志配置
```toml
[logging]
level = "info"
max_file_size_mb = 50
max_files = 60
log_dir = "logs"
enable_console = false
cleanup_days = 30
```

## 📊 监控和维护

### 健康检查
```bash
# API健康检查
curl http://localhost:8080/api/admin/status

# 服务状态检查
sc query ProductDataSync
```

### 日志监控
```bash
# 查看最新日志
Get-Content "logs\app.log" -Tail 50 -Wait

# 查看错误日志
Get-Content "logs\error.log" -Tail 20
```

### 性能监控
```bash
# 查看进程资源使用
Get-Process product-data-sync | Select-Object CPU,WorkingSet,VirtualMemorySize

# 查看端口监听
netstat -an | findstr :8080
```

## 🔒 安全配置

### 防火墙设置
```bash
# 允许8080端口
netsh advfirewall firewall add rule name="Product Data Sync" dir=in action=allow protocol=TCP localport=8080
```

### 服务账户
- 推荐使用专用服务账户
- 最小权限原则
- 定期更新密码

### SSL/TLS配置
```toml
[server]
tls_cert = "path/to/cert.pem"
tls_key = "path/to/key.pem"
```

## 🚨 故障排除

### 常见问题

1. **DPI-1047错误**
   - 检查Oracle客户端安装
   - 验证环境变量设置
   - 重启服务

2. **端口占用**
   ```bash
   netstat -ano | findstr :8080
   taskkill /PID <PID> /F
   ```

3. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置
   - 查看防火墙设置

4. **服务启动失败**
   - 检查日志文件
   - 验证权限设置
   - 确认依赖项安装

### 日志分析
```bash
# 查找错误
findstr /i "error" logs\app.log

# 查找连接问题
findstr /i "connection" logs\database.log
```

## 📈 性能优化

### 数据库连接池
```toml
[oracle]
pool_size = 10
connection_timeout = 30

[mongodb]
pool_size = 10
connection_timeout = 30
```

### 同步频率调整
```toml
[sync]
interval_seconds = 300  # 根据业务需求调整
batch_size = 50         # 根据数据量调整
```

### 资源限制
```bash
# 使用Docker限制资源
docker run --memory=1g --cpus=2 product-data-sync
```

## 🔄 更新部署

### 滚动更新
1. 构建新版本
2. 停止服务
3. 备份当前版本
4. 部署新版本
5. 启动服务
6. 验证功能

### 回滚策略
```bash
# 备份当前版本
copy target\release\product-data-sync.exe target\release\product-data-sync.exe.backup

# 回滚到备份版本
copy target\release\product-data-sync.exe.backup target\release\product-data-sync.exe
```
