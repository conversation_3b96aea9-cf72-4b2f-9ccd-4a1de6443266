use actix_web::{post, web, HttpResponse};
use serde::Deserialize;
use crate::db::{OracleClient, MongoClient};
use log::{info, error};

#[derive(Deserialize)]
pub struct PalletQueryRequest {
    pallet_no: String,
}

#[derive(Deserialize)]
pub struct LotNumberQueryRequest {
    lot_number: String,
}

#[post("/api/modules/query-by-pallet")]
pub async fn query_modules_by_pallet(
    oracle_client: web::Data<OracleClient>,
    mongo_client: web::Data<MongoClient>,
    query: web::Json<PalletQueryRequest>,
) -> HttpResponse {
    // 查询 Oracle 数据
    let modules = match oracle_client.query_modules_by_pallet(&query.pallet_no) {
        Ok(modules) => modules,
        Err(e) => {
            error!("Pallet query failed: {}", e);
            return HttpResponse::InternalServerError().json(format!("Query failed: {}", e));
        }
    };

    // 保存到 MongoDB
    if !modules.is_empty() {
        if let Err(e) = mongo_client.save_modules_with_upsert(modules.clone()).await {
            error!("Failed to save modules to MongoDB: {}", e);
            return HttpResponse::InternalServerError().json("Failed to save data");
        }
        info!("Successfully upserted {} modules to MongoDB", modules.len());
    }

    HttpResponse::Ok().json(modules)
}

#[post("/api/modules/query-by-lot-number")]
pub async fn query_modules_by_lot_number(
    oracle_client: web::Data<OracleClient>,
    mongo_client: web::Data<MongoClient>,
    query: web::Json<LotNumberQueryRequest>,
) -> HttpResponse {
    // 查询 Oracle 数据
    let modules = match oracle_client.query_modules_by_lot_number(&query.lot_number) {
        Ok(modules) => modules,
        Err(e) => {
            error!("Lot number query failed: {}", e);
            return HttpResponse::InternalServerError().json(format!("Query failed: {}", e));
        }
    };

    // 保存到 MongoDB
    if !modules.is_empty() {
        if let Err(e) = mongo_client.save_modules_with_upsert(modules.clone()).await {
            error!("Failed to save modules to MongoDB: {}", e);
            return HttpResponse::InternalServerError().json("Failed to save data");
        }
        info!("Successfully upserted {} modules to MongoDB", modules.len());
    }

    HttpResponse::Ok().json(modules)
}