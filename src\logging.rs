// src/logging.rs
// 改进的日志管理模块

use anyhow::Result;
use log::LevelFilter;
use log4rs::{
    append::{
        console::{ConsoleAppender, Target},
        rolling_file::{
            policy::compound::{
                roll::fixed_window::FixedWindowRoller,
                trigger::size::Si<PERSON><PERSON>rigger,
                CompoundPolicy,
            },
            RollingFileAppender,
        },
    },
    config::{Appender, Config, Logger, Root},
    encode::pattern::PatternEncoder,
    filter::threshold::ThresholdFilter,
};
use std::path::Path;

/// 日志配置结构
pub struct LogConfig {
    pub level: LevelFilter,
    pub max_file_size: u64,      // 单个文件最大大小（字节）
    pub max_files: u32,          // 保留的历史文件数量
    pub log_dir: String,         // 日志目录
    pub enable_console: bool,    // 是否启用控制台输出
}

impl Default for LogConfig {
    fn default() -> Self {
        Self {
            level: LevelFilter::Info,
            max_file_size: 10 * 1024 * 1024, // 10MB
            max_files: 30,                    // 保留30个文件
            log_dir: "logs".to_string(),
            enable_console: true,
        }
    }
}

/// 初始化日志系统
pub fn init_logging(config: Option<LogConfig>) -> Result<()> {
    let config = config.unwrap_or_default();

    // 确保日志目录存在
    std::fs::create_dir_all(&config.log_dir)?;

    let pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} [{l}] {t} - {m}{n}";

    // 控制台输出
    let mut appenders = Vec::new();
    let mut root_appenders = Vec::new();

    if config.enable_console {
        let console = ConsoleAppender::builder()
            .target(Target::Stdout)
            .encoder(Box::new(PatternEncoder::new(pattern)))
            .build();

        appenders.push(Appender::builder()
            .filter(Box::new(ThresholdFilter::new(LevelFilter::Info)))
            .build("console", Box::new(console)));
        root_appenders.push("console");
    }

    // 应用日志文件
    let app_log_path = format!("{}/app.log", config.log_dir);
    let app_roller = FixedWindowRoller::builder()
        .build(&format!("{}/app.{{}}.log", config.log_dir), config.max_files)?;

    let app_trigger = SizeTrigger::new(config.max_file_size);
    let app_policy = CompoundPolicy::new(Box::new(app_trigger), Box::new(app_roller));

    let app_appender = RollingFileAppender::builder()
        .encoder(Box::new(PatternEncoder::new(pattern)))
        .build(app_log_path, Box::new(app_policy))?;

    appenders.push(Appender::builder()
        .build("app_file", Box::new(app_appender)));
    root_appenders.push("app_file");

    // 错误日志文件（只记录错误和警告）
    let error_log_path = format!("{}/error.log", config.log_dir);
    let error_roller = FixedWindowRoller::builder()
        .build(&format!("{}/error.{{}}.log", config.log_dir), config.max_files * 2)?; // 错误日志保留更久

    let error_trigger = SizeTrigger::new(config.max_file_size / 2); // 错误日志文件更小
    let error_policy = CompoundPolicy::new(Box::new(error_trigger), Box::new(error_roller));

    let error_appender = RollingFileAppender::builder()
        .encoder(Box::new(PatternEncoder::new(pattern)))
        .build(error_log_path, Box::new(error_policy))?;

    appenders.push(Appender::builder()
        .filter(Box::new(ThresholdFilter::new(LevelFilter::Warn)))
        .build("error_file", Box::new(error_appender)));

    // 数据库操作日志
    let db_log_path = format!("{}/database.log", config.log_dir);
    let db_roller = FixedWindowRoller::builder()
        .build(&format!("{}/database.{{}}.log", config.log_dir), config.max_files / 2)?; // 数据库日志保留较少

    let db_trigger = SizeTrigger::new(config.max_file_size * 2); // 数据库日志文件可以更大
    let db_policy = CompoundPolicy::new(Box::new(db_trigger), Box::new(db_roller));

    let db_appender = RollingFileAppender::builder()
        .encoder(Box::new(PatternEncoder::new(pattern)))
        .build(db_log_path, Box::new(db_policy))?;

    appenders.push(Appender::builder()
        .build("db_file", Box::new(db_appender)));

    // 构建配置
    let mut config_builder = Config::builder();

    // 添加所有appender
    for appender in appenders {
        config_builder = config_builder.appender(appender);
    }

    // 配置特定模块的日志级别
    let mut loggers = Vec::new();

    // 数据库模块 - 降低日志级别减少输出
    loggers.push(
        Logger::builder()
            .appender("db_file")
            .appender("error_file")
            .additive(false)
            .build("product_data_sync::db", LevelFilter::Warn)
    );

    // Oracle查询模块 - 只记录重要信息
    loggers.push(
        Logger::builder()
            .appender("db_file")
            .additive(false)
            .build("product_data_sync::db::oracle", LevelFilter::Error)
    );

    // 同步任务模块
    loggers.push(
        Logger::builder()
            .appender("app_file")
            .additive(false)
            .build("product_data_sync::tasks", LevelFilter::Info)
    );

    // HTTP路由模块
    loggers.push(
        Logger::builder()
            .appender("app_file")
            .additive(false)
            .build("product_data_sync::routes", LevelFilter::Info)
    );

    // 添加所有logger
    for logger in loggers {
        config_builder = config_builder.logger(logger);
    }

    // 根配置
    let root = Root::builder()
        .appenders(root_appenders)
        .build(config.level);

    let log_config = config_builder.build(root)?;

    log4rs::init_config(log_config)?;

    log::info!("日志系统初始化完成");
    log::info!("日志目录: {}", config.log_dir);
    log::info!("最大文件大小: {}MB", config.max_file_size / 1024 / 1024);
    log::info!("保留文件数量: {}", config.max_files);

    Ok(())
}

/// 清理旧日志文件
pub fn cleanup_old_logs(log_dir: &str, days_to_keep: u64) -> Result<()> {
    use std::time::{Duration, SystemTime};

    let log_path = Path::new(log_dir);
    if !log_path.exists() {
        return Ok(());
    }

    let cutoff_time = SystemTime::now() - Duration::from_secs(days_to_keep * 24 * 3600);
    let mut deleted_count = 0;

    for entry in std::fs::read_dir(log_path)? {
        let entry = entry?;
        let path = entry.path();

        if path.is_file() {
            if let Ok(metadata) = entry.metadata() {
                if let Ok(modified) = metadata.modified() {
                    if modified < cutoff_time {
                        if let Err(e) = std::fs::remove_file(&path) {
                            log::warn!("删除旧日志文件失败: {:?}, 错误: {}", path, e);
                        } else {
                            deleted_count += 1;
                            log::info!("删除旧日志文件: {:?}", path);
                        }
                    }
                }
            }
        }
    }

    if deleted_count > 0 {
        log::info!("清理完成，删除了 {} 个旧日志文件", deleted_count);
    }

    Ok(())
}

/// 获取日志文件大小统计
pub fn get_log_stats(log_dir: &str) -> Result<LogStats> {
    let log_path = Path::new(log_dir);
    let mut stats = LogStats::default();

    if !log_path.exists() {
        return Ok(stats);
    }

    for entry in std::fs::read_dir(log_path)? {
        let entry = entry?;
        let path = entry.path();

        if path.is_file() {
            if let Ok(metadata) = entry.metadata() {
                stats.total_files += 1;
                stats.total_size += metadata.len();

                if metadata.len() > stats.largest_file_size {
                    stats.largest_file_size = metadata.len();
                    stats.largest_file_name = path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("unknown")
                        .to_string();
                }
            }
        }
    }

    Ok(stats)
}

#[derive(Debug, Default)]
pub struct LogStats {
    pub total_files: u32,
    pub total_size: u64,
    pub largest_file_size: u64,
    pub largest_file_name: String,
}

impl LogStats {
    pub fn total_size_mb(&self) -> f64 {
        self.total_size as f64 / 1024.0 / 1024.0
    }

    pub fn largest_file_size_mb(&self) -> f64 {
        self.largest_file_size as f64 / 1024.0 / 1024.0
    }
}
