# 改进的日志配置文件
# 使用 log4rs 的配置文件格式

# 刷新间隔（秒）
refresh_rate = 30

# 定义输出格式
[appenders.stdout]
kind = "console"
[appenders.stdout.encoder]
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} [{l}] {t} - {m}{n}"

# 文件输出 - 应用日志
[appenders.file_app]
kind = "rolling_file"
path = "logs/app.log"
policy = "compound"

[appenders.file_app.encoder]
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} [{l}] {t} - {m}{n}"

# 滚动策略：按大小和时间
[appenders.file_app.policy.trigger.size]
limit = "10MB"  # 单个文件最大10MB

[appenders.file_app.policy.trigger.time]
interval = "1 day"  # 每天轮转

[appenders.file_app.policy.roller.pattern]
pattern = "logs/app.{}.log"  # 归档文件命名模式

[appenders.file_app.policy.roller.count]
count = 30  # 保留30个历史文件

# 文件输出 - 错误日志
[appenders.file_error]
kind = "rolling_file"
path = "logs/error.log"
policy = "compound"

[appenders.file_error.encoder]
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} [{l}] {t} - {m}{n}"

[appenders.file_error.policy.trigger.size]
limit = "5MB"

[appenders.file_error.policy.trigger.time]
interval = "1 day"

[appenders.file_error.policy.roller.pattern]
pattern = "logs/error.{}.log"

[appenders.file_error.policy.roller.count]
count = 60  # 错误日志保留更久

# 文件输出 - 数据库操作日志
[appenders.file_db]
kind = "rolling_file"
path = "logs/database.log"
policy = "compound"

[appenders.file_db.encoder]
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} [{l}] {t} - {m}{n}"

[appenders.file_db.policy.trigger.size]
limit = "20MB"  # 数据库日志可能较大

[appenders.file_db.policy.trigger.time]
interval = "1 day"

[appenders.file_db.policy.roller.pattern]
pattern = "logs/database.{}.log"

[appenders.file_db.policy.roller.count]
count = 15

# 根日志配置
[root]
level = "info"
appenders = ["stdout", "file_app"]

# 模块特定配置
[loggers]

# 数据库相关日志
[loggers."product_data_sync::db"]
level = "info"
appenders = ["file_db"]
additive = false

# 错误日志单独记录
[loggers."product_data_sync::error"]
level = "error"
appenders = ["file_error", "stdout"]
additive = false

# Oracle查询日志 - 降低级别减少输出
[loggers."product_data_sync::db::oracle"]
level = "warn"  # 只记录警告和错误
appenders = ["file_db"]
additive = false

# 同步任务日志
[loggers."product_data_sync::tasks"]
level = "info"
appenders = ["file_app"]
additive = false

# HTTP请求日志
[loggers."product_data_sync::routes"]
level = "info"
appenders = ["file_app"]
additive = false
