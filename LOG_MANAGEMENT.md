# 日志管理系统说明

## 问题分析

### 原有日志系统的问题
1. **日志文件无限增长**：没有文件大小限制，可能导致磁盘空间耗尽
2. **缺乏轮转策略**：只按日期分割，单个文件可能过大
3. **历史文件累积**：旧日志文件永不删除，占用大量存储空间
4. **日志级别过于详细**：大量info级别日志，包括每次查询的详细信息
5. **双重日志系统**：env_logger和log4rs混用，可能导致冲突

## 改进方案

### 1. 统一日志系统
- 使用 `log4rs` 作为唯一的日志框架
- 移除 `env_logger` 的使用
- 提供统一的配置接口

### 2. 文件大小和轮转管理
```toml
[logging]
level = "info"                # 日志级别
max_file_size_mb = 10         # 单个文件最大10MB
max_files = 30                # 保留30个历史文件
log_dir = "logs"              # 日志目录
enable_console = true         # 控制台输出
cleanup_days = 30             # 自动清理30天前的文件
```

### 3. 分类日志文件
- **app.log**: 应用主日志（10MB轮转，保留30个文件）
- **error.log**: 错误日志（5MB轮转，保留60个文件）
- **database.log**: 数据库操作日志（20MB轮转，保留15个文件）

### 4. 日志级别优化
- **数据库模块**: 从 `info` 降级到 `warn`，减少查询日志
- **Oracle查询**: 从 `info` 降级到 `error`，只记录错误
- **应用主流程**: 保持 `info` 级别
- **错误处理**: 单独记录到错误日志

## 配置说明

### 配置文件 (config/default.toml)
```toml
[logging]
level = "info"                # trace, debug, info, warn, error
max_file_size_mb = 10         # 单个日志文件最大大小(MB)
max_files = 30                # 保留的历史文件数量
log_dir = "logs"              # 日志目录
enable_console = true         # 是否启用控制台输出
cleanup_days = 30             # 自动清理多少天前的日志文件
```

### 环境变量覆盖
```bash
export APP_LOGGING_LEVEL=warn
export APP_LOGGING_MAX_FILE_SIZE_MB=5
export APP_LOGGING_MAX_FILES=20
```

## 日志文件结构

```
logs/
├── app.log              # 当前应用日志
├── app.1.log            # 历史应用日志
├── app.2.log
├── ...
├── error.log            # 当前错误日志
├── error.1.log          # 历史错误日志
├── ...
├── database.log         # 当前数据库日志
└── database.1.log       # 历史数据库日志
```

## 管理接口

### 1. 获取日志统计
```bash
GET /api/admin/logs/stats
```

响应示例：
```json
{
  "total_files": 15,
  "total_size_mb": 45.6,
  "largest_file_name": "database.log",
  "largest_file_size_mb": 8.9,
  "log_directory": "logs"
}
```

### 2. 清理旧日志
```bash
POST /api/admin/logs/cleanup
Content-Type: application/json

{
  "days_to_keep": 30
}
```

### 3. 系统状态
```bash
GET /api/admin/status
```

## 存储空间估算

### 默认配置下的存储使用
- **应用日志**: 10MB × 30个文件 = 300MB
- **错误日志**: 5MB × 60个文件 = 300MB  
- **数据库日志**: 20MB × 15个文件 = 300MB
- **总计**: 约900MB最大存储空间

### 生产环境建议
```toml
[logging]
level = "warn"               # 减少日志量
max_file_size_mb = 20        # 增大单文件大小
max_files = 20               # 减少保留文件数
cleanup_days = 15            # 更频繁清理
```

## 监控和维护

### 1. 定期检查
- 使用管理接口监控日志文件大小
- 设置磁盘空间告警
- 定期检查日志轮转是否正常

### 2. 性能优化
- 根据实际情况调整日志级别
- 监控日志写入对性能的影响
- 考虑异步日志写入

### 3. 故障排查
- 错误日志单独存储，便于问题定位
- 保留足够的历史日志用于分析
- 重要操作增加结构化日志

## 迁移步骤

1. **备份现有日志**
2. **更新配置文件**：添加logging配置段
3. **重启应用**：新的日志系统生效
4. **验证功能**：检查日志文件生成和轮转
5. **清理旧文件**：手动清理旧格式的日志文件

## 注意事项

1. **磁盘空间**：确保有足够空间存储日志文件
2. **权限设置**：确保应用有权限创建和写入日志目录
3. **备份策略**：重要日志应纳入备份计划
4. **安全考虑**：日志文件可能包含敏感信息，注意访问控制
