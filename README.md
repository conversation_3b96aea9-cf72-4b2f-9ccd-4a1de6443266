# Product Data Sync

一个用于在Oracle数据库和MongoDB之间同步产品数据的Rust应用程序。

## 📋 目录

- [功能特性](#功能特性)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [API接口](#api接口)
- [部署指南](#部署指南)
- [配置说明](#配置说明)
- [日志管理](#日志管理)
- [故障排除](#故障排除)
- [开发指南](#开发指南)

## 🚀 功能特性

### 核心功能
- ✅ **数据同步**：Oracle数据库到MongoDB的自动同步
- ✅ **RESTful API**：提供产品数据查询接口
- ✅ **连接池管理**：支持Oracle和MongoDB连接池
- ✅ **配置化同步**：可配置的同步间隔和批次大小

### 新增功能
- 🆕 **根据组件号查询**：支持通过组件号直接查询组件信息
- 🆕 **日志管理系统**：文件大小控制、自动轮转、历史清理
- 🆕 **管理接口**：日志统计、系统状态、日志清理
- 🆕 **多种部署方式**：Windows服务、Docker、任务计划程序
- 🆕 **生产环境优化**：性能调优、监控、故障恢复

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Oracle DB     │    │  Product Data   │    │   MongoDB       │
│                 │◄──►│     Sync        │◄──►│                 │
│ (Source Data)   │    │   Application   │    │ (Target Data)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   REST API      │
                       │                 │
                       │ • 托盘号查询     │
                       │ • 组件号查询     │
                       │ • 系统管理      │
                       └─────────────────┘
```

## ⚡ 快速开始

### 系统要求
- **操作系统**：Windows 10+ / Windows Server 2016+
- **Rust**：1.82+ (推荐最新稳定版)
- **Oracle Instant Client**：21.x 或 23.x
- **MongoDB**：4.4+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd product-data-sync
   ```

2. **安装Oracle Instant Client**
   ```bash
   # 下载并解压到指定目录
   # 例如：D:\0.MES\0.ANewCode\instantclient_23_6
   ```

3. **配置环境变量**
   ```bash
   # 设置系统环境变量
   setx OCI_LIB_DIR "D:\0.MES\0.ANewCode\instantclient_23_6" /M
   setx ORACLE_HOME "D:\0.MES\0.ANewCode\instantclient_23_6" /M
   setx PATH "D:\0.MES\0.ANewCode\instantclient_23_6;%PATH%" /M
   ```

4. **配置数据库连接**
   ```bash
   # 复制并编辑配置文件
   copy config\default.toml.example config\default.toml
   # 编辑 config\default.toml 中的数据库连接信息
   ```

5. **构建和运行**
   ```bash
   # 开发环境运行
   cargo run

   # 或使用启动脚本（推荐）
   .\start_app.bat
   ```

## 🌐 API接口

### 组件查询接口

#### 1. 根据托盘号查询组件
```http
POST /api/modules/query-by-pallet
Content-Type: application/json

{
    "pallet_no": "6100001505-3-0079"
}
```

#### 2. 根据组件号查询组件 🆕
```http
POST /api/modules/query-by-lot-number
Content-Type: application/json

{
    "lot_number": "LOT123456789"
}
```

### 管理接口 🆕

#### 3. 获取日志统计
```http
GET /api/admin/logs/stats
```

#### 4. 清理旧日志
```http
POST /api/admin/logs/cleanup
Content-Type: application/json

{
    "days_to_keep": 30
}
```

#### 5. 系统状态
```http
GET /api/admin/status
```

### 响应示例
```json
[
    {
        "CreateDate": "2024-01-15T10:30:00Z",
        "Factory": "HN_工厂名称",
        "FillFactor": "85.50",
        "LotNumber": "LOT123456789",
        "MaximumCurrent": "10.25",
        "MaximumPower": "450.00",
        "MaximumVoltage": "43.90",
        "ModuleName": "组件型号",
        "OpenCircuitVoltage": "48.50",
        "ProductDate": "2024-01-15T08:00:00Z",
        "ShortCircuitCurrent": "11.20"
    }
]
```

## 🚀 部署指南

### 开发环境
```bash
# 使用启动脚本
.\start_app.bat

# 或手动设置环境变量后运行
$env:OCI_LIB_DIR = "D:\0.MES\0.ANewCode\instantclient_23_6"
$env:PATH = "D:\0.MES\0.ANewCode\instantclient_23_6;" + $env:PATH
cargo run
```

### 生产环境部署

#### 方案1：Windows服务部署（推荐）
```bash
# 以管理员身份运行
.\deploy_server.bat
```

**特点**：
- ✅ 系统集成，自动启动
- ✅ 故障自动恢复
- ✅ 完整的日志记录
- ✅ 服务管理界面

#### 方案2：NSSM部署
```bash
# 下载NSSM后运行
.\deploy_with_nssm.bat
```

#### 方案3：任务计划程序
```bash
.\deploy_task_scheduler.bat
```

#### 方案4：Docker部署
```bash
.\deploy_docker.bat
```

### 服务管理
```bash
# 启动服务
sc start ProductDataSync

# 停止服务
sc stop ProductDataSync

# 查看状态
sc query ProductDataSync

# 查看日志
Get-Content "logs\app.log" -Tail 50 -Wait
```

## ⚙️ 配置说明

### 数据库配置
```toml
[oracle]
host = "*************"
port = 1521
database = "HNZJMESDG"
username = "HNZJMESDB"
password = "Sie$2023Camstar"
pool_size = 10

[mongodb]
uri = "mongodb://localhost:27017"
database = "product_sync"
collection = "solar_modules"
pool_size = 10
```

### 同步配置
```toml
[sync]
interval_seconds = 30       # 同步间隔（秒）
batch_size = 10            # 批次大小
data_delay_days = 7        # 数据延迟天数，只同步N天前的数据，7天内的数据暂不同步
```

### 日志配置 🆕
```toml
[logging]
level = "info"              # 日志级别
max_file_size_mb = 10       # 单文件最大大小
max_files = 30              # 保留文件数量
log_dir = "logs"            # 日志目录
enable_console = true       # 控制台输出
cleanup_days = 30           # 自动清理天数
```

## 📊 日志管理

### 日志文件结构
```
logs/
├── app.log              # 应用主日志
├── app.1.log            # 历史应用日志
├── error.log            # 错误日志
├── error.1.log          # 历史错误日志
├── database.log         # 数据库操作日志
└── database.1.log       # 历史数据库日志
```

### 日志特性
- ✅ **自动轮转**：文件达到大小限制自动轮转
- ✅ **分类存储**：应用、错误、数据库日志分离
- ✅ **自动清理**：定期清理过期日志文件
- ✅ **大小控制**：可配置的文件大小和数量限制

### 日志查看
```bash
# 查看实时日志
Get-Content "logs\app.log" -Tail 50 -Wait

# 查看错误日志
Get-Content "logs\error.log" -Tail 20

# 查看日志统计
curl http://localhost:8080/api/admin/logs/stats
```

## 🔧 故障排除

### 常见问题

#### 1. Oracle连接问题
**错误**：`DPI-1047: Cannot locate a 64-bit Oracle Client library`

**解决方案**：
```bash
# 检查Oracle客户端安装
Test-Path "D:\0.MES\0.ANewCode\instantclient_23_6\oci.dll"

# 检查环境变量
echo $env:OCI_LIB_DIR
echo $env:PATH | Select-String "instantclient"

# 使用启动脚本
.\start_app.bat
```

#### 2. 端口占用
**错误**：`Address already in use`

**解决方案**：
```bash
# 查找占用进程
netstat -ano | findstr :8080

# 终止进程
taskkill /PID <PID> /F
```

#### 3. 数据库连接超时
**解决方案**：
```bash
# 检查网络连接
Test-NetConnection -ComputerName ************* -Port 1521

# 检查防火墙设置
# 调整连接超时配置
```

### 日志分析
```bash
# 查找错误
findstr /i "error" logs\app.log

# 查找连接问题
findstr /i "connection" logs\database.log

# 查看最近的警告
findstr /i "warn" logs\app.log
```

## 👨‍💻 开发指南

### 项目结构
```
src/
├── main.rs              # 主程序入口
├── config/              # 配置管理
│   └── mod.rs
├── db/                  # 数据库客户端
│   ├── mod.rs
│   ├── oracle.rs        # Oracle客户端
│   └── mongodb.rs       # MongoDB客户端
├── models/              # 数据模型
│   └── mod.rs
├── routes/              # API路由
│   ├── mod.rs
│   ├── module.rs        # 组件查询接口
│   └── admin.rs         # 管理接口
├── tasks/               # 后台任务
│   └── mod.rs
└── logging.rs           # 日志管理
```

### 开发环境设置
```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 安装依赖
cargo build

# 运行测试
cargo test

# 代码格式化
cargo fmt

# 代码检查
cargo clippy
```

### 添加新功能
1. **新增API接口**：在 `src/routes/` 中添加新的路由模块
2. **数据库操作**：在 `src/db/` 中添加新的查询方法
3. **数据模型**：在 `src/models/` 中定义新的数据结构
4. **配置选项**：在 `src/config/` 中添加新的配置项

### 测试
```bash
# 运行所有测试
cargo test

# 运行特定测试
cargo test test_oracle_connection

# 集成测试
cargo test --test integration

# API测试
python test_api.py
```

## 📈 性能优化

### 数据库连接池
```toml
[oracle]
pool_size = 10              # 根据并发需求调整
connection_timeout = 30     # 连接超时时间

[mongodb]
pool_size = 10
connection_timeout = 30
```

### 同步性能
```toml
[sync]
interval_seconds = 300      # 生产环境建议5分钟
batch_size = 50            # 根据数据量调整
data_delay_days = 7        # 数据延迟天数，只同步N天前的数据
```

### 日志性能
```toml
[logging]
level = "warn"             # 生产环境降低日志级别
max_file_size_mb = 20      # 增大文件大小减少轮转
enable_console = false     # 生产环境关闭控制台输出
```

## 📝 更新日志

### v2.1.0 (最新)
- 🆕 **数据延迟同步控制**：新增可配置的数据延迟天数，只同步N天前的数据
- 🔧 优化同步逻辑，支持基于时间窗口的数据过滤机制
- 📚 更新配置文档和性能优化建议

### v2.0.0
- 🆕 新增根据组件号查询接口
- 🆕 完整的日志管理系统
- 🆕 管理接口（日志统计、系统状态）
- 🆕 多种部署方案
- 🆕 生产环境优化
- 🔧 修复Oracle连接环境变量问题
- 📚 完善文档和部署指南

### v1.0.0
- ✅ 基础数据同步功能
- ✅ 根据托盘号查询接口
- ✅ Oracle和MongoDB连接池
- ✅ 配置文件管理

## 📞 技术支持

### 文档资源
- [API使用说明](API_USAGE.md)
- [服务器部署指南](SERVER_DEPLOYMENT.md)
- [Oracle客户端安装](ORACLE_CLIENT_SETUP.md)
- [日志管理说明](LOG_MANAGEMENT.md)
- [故障排除指南](ORACLE_TROUBLESHOOTING.md)

### 联系方式
- 项目地址：[GitHub Repository]
- 问题反馈：[Issues]
- 技术文档：[Wiki]

---

**注意**：本项目专为MES系统的产品数据同步而设计，确保在生产环境中正确配置Oracle客户端和网络连接。