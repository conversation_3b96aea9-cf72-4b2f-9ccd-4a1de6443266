@echo off
echo ===== Product Data Sync Docker Deployment =====

set IMAGE_NAME=product-data-sync
set CONTAINER_NAME=product-data-sync-container
set APP_DIR=D:\0.MES\0.ANewCode\product-data-sync

echo 1. Checking Docker...
docker --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Docker is not installed or not in PATH
    echo Please install Docker Desktop for Windows
    pause
    exit /b 1
)

echo 2. Building release version...
cd /d "%APP_DIR%"
cargo build --release
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo 3. Stopping existing container...
docker stop %CONTAINER_NAME% >nul 2>&1
docker rm %CONTAINER_NAME% >nul 2>&1

echo 4. Building Docker image...
docker build -f Dockerfile.windows -t %IMAGE_NAME% .
if %errorLevel% neq 0 (
    echo ERROR: Docker build failed
    pause
    exit /b 1
)

echo 5. Running container...
docker run -d ^
    --name %CONTAINER_NAME% ^
    -p 8080:8080 ^
    --restart unless-stopped ^
    %IMAGE_NAME%

if %errorLevel% neq 0 (
    echo ERROR: Failed to start container
    pause
    exit /b 1
)

echo 6. Checking container status...
timeout /t 5 /nobreak >nul
docker ps | findstr %CONTAINER_NAME%

echo.
echo ===== Docker Deployment Complete =====
echo Container Name: %CONTAINER_NAME%
echo Image Name: %IMAGE_NAME%
echo Port: 8080
echo.
echo Docker Management:
echo   Status:  docker ps
echo   Logs:    docker logs %CONTAINER_NAME%
echo   Stop:    docker stop %CONTAINER_NAME%
echo   Start:   docker start %CONTAINER_NAME%
echo   Remove:  docker rm %CONTAINER_NAME%
echo.

pause
