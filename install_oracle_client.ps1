# Oracle Instant Client 安装脚本
# 需要管理员权限运行

param(
    [string]$InstallPath = "C:\oracle\instantclient_21_13",
    [switch]$SkipDownload = $false
)

Write-Host "=== Oracle Instant Client 安装脚本 ===" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误: 需要管理员权限运行此脚本" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    exit 1
}

# 创建安装目录
Write-Host "创建安装目录: $InstallPath" -ForegroundColor Yellow
if (!(Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
}

# 下载URL（需要Oracle账户登录，这里提供手动下载说明）
$downloadUrl = "https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html"

if (!$SkipDownload) {
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host "手动下载步骤:" -ForegroundColor Yellow
    Write-Host "1. 访问: $downloadUrl" -ForegroundColor White
    Write-Host "2. 下载 'Basic Package' (instantclient-basic-windows.x64-*********.0dbru.zip)" -ForegroundColor White
    Write-Host "3. 将下载的文件解压到: $InstallPath" -ForegroundColor White
    Write-Host "4. 解压后重新运行此脚本: .\install_oracle_client.ps1 -SkipDownload" -ForegroundColor White
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    # 尝试打开下载页面
    try {
        Start-Process $downloadUrl
        Write-Host "已打开下载页面，请手动下载并解压文件" -ForegroundColor Green
    } catch {
        Write-Host "无法打开浏览器，请手动访问上述URL" -ForegroundColor Yellow
    }
    
    Write-Host "下载完成后，请重新运行: .\install_oracle_client.ps1 -SkipDownload" -ForegroundColor Green
    exit 0
}

# 检查必要的DLL文件是否存在
$requiredFiles = @("oci.dll", "oraociei21.dll", "oraocci21.dll")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    $filePath = Join-Path $InstallPath $file
    if (!(Test-Path $filePath)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "错误: 以下必要文件缺失:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
    Write-Host "请确保已正确解压Oracle Instant Client到: $InstallPath" -ForegroundColor Yellow
    exit 1
}

Write-Host "检查完成，所有必要文件都存在" -ForegroundColor Green

# 配置环境变量
Write-Host "配置环境变量..." -ForegroundColor Yellow

# 获取当前PATH
$currentPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)

# 检查是否已经包含Oracle路径
if ($currentPath -notlike "*$InstallPath*") {
    Write-Host "添加到系统PATH: $InstallPath" -ForegroundColor Yellow
    $newPath = $currentPath + ";" + $InstallPath
    [Environment]::SetEnvironmentVariable("Path", $newPath, [EnvironmentVariableTarget]::Machine)
    Write-Host "PATH环境变量已更新" -ForegroundColor Green
} else {
    Write-Host "PATH中已包含Oracle路径" -ForegroundColor Green
}

# 设置ORACLE_HOME（可选）
[Environment]::SetEnvironmentVariable("ORACLE_HOME", $InstallPath, [EnvironmentVariableTarget]::Machine)
Write-Host "ORACLE_HOME已设置为: $InstallPath" -ForegroundColor Green

# 验证安装
Write-Host "验证安装..." -ForegroundColor Yellow

# 刷新当前会话的环境变量
$env:Path = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)

# 检查DLL是否可以找到
try {
    $oracleDll = Join-Path $InstallPath "oci.dll"
    if (Test-Path $oracleDll) {
        Write-Host "✓ Oracle客户端库文件存在" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ 无法验证Oracle客户端库" -ForegroundColor Red
}

Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "安装完成!" -ForegroundColor Green
Write-Host "请重新启动PowerShell或命令提示符，然后运行:" -ForegroundColor Yellow
Write-Host "  cargo run" -ForegroundColor White
Write-Host "=" * 60 -ForegroundColor Cyan

# 提供测试连接的建议
Write-Host ""
Write-Host "测试连接建议:" -ForegroundColor Yellow
Write-Host "1. 重新打开PowerShell" -ForegroundColor White
Write-Host "2. 进入项目目录: cd $PWD" -ForegroundColor White
Write-Host "3. 运行程序: cargo run" -ForegroundColor White
Write-Host "4. 如果仍有问题，请检查网络连接和数据库配置" -ForegroundColor White

Write-Host ""
Write-Host "如果遇到问题，请查看 ORACLE_CLIENT_SETUP.md 获取详细说明" -ForegroundColor Cyan
