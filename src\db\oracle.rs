use crate::config::OracleConfig;
use crate::models::SolarModule;
use anyhow::Result;
use chrono::{DateTime as ChronoDateTime, NaiveDateTime, Utc};
use log::{info, LevelFilter};
use log4rs::{
    append::file::FileAppender,
    config::{Appender, Config, Root},
    encode::pattern::PatternEncoder,
};
use mongodb::bson::DateTime;
use r2d2::Pool;
use r2d2_oracle::OracleConnectionManager;
use std::time::{Duration, UNIX_EPOCH};

pub fn get_log_filename() -> String {
    let now = chrono::Local::now();
    format!("logs/sync_{}.log", now.format("%Y-%m-%d"))
}

pub fn init_logger() -> Result<()> {
    let log_file = get_log_filename();

    // 确保日志目录存在
    std::fs::create_dir_all("logs")?;

    let file_appender = FileAppender::builder()
        .encoder(Box::new(PatternEncoder::new(
            "{d(%Y-%m-%d %H:%M:%S%.3f)} [{l}] {t} - {m}{n}",
        )))
        .build(log_file)?;

    let config = Config::builder()
        .appender(Appender::builder().build("file", Box::new(file_appender)))
        .build(Root::builder().appender("file").build(LevelFilter::Info))?;

    log4rs::init_config(config)?;
    Ok(())
}

#[derive(Clone)]
pub struct OracleClient {
    pool: Pool<OracleConnectionManager>,
}

impl OracleClient {
    pub fn new(config: &OracleConfig) -> Result<Self> {
        // 使用服务名的连接字符串格式
        let connection_string = format!(
            "(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={})(PORT={}))(CONNECT_DATA=(SERVICE_NAME={})))",
            config.host,
            config.port,
            config.database  // 这里的 database 实际上是服务名
        );

        let manager =
            OracleConnectionManager::new(&config.username, &config.password, &connection_string);

        let pool = Pool::new(manager)?;

        Ok(Self { pool })
    }

    pub fn query_modules(
        &self,
        since_date: DateTime,
        last_lot_number: Option<String>
    ) -> Result<(Vec<SolarModule>, Option<DateTime>)> {
        // 调用带截止时间的方法，不设置截止时间限制
        self.query_modules_with_cutoff(since_date, last_lot_number, None)
    }

    pub fn query_modules_with_cutoff(
        &self,
        since_date: DateTime,
        last_lot_number: Option<String>,
        cutoff_date: Option<DateTime>
    ) -> Result<(Vec<SolarModule>, Option<DateTime>)> {
        // 添加参数日志
        info!(
            "Querying modules with params - since_date: {:?}, last_lot_number: {:?}, cutoff_date: {:?}",
            since_date, last_lot_number, cutoff_date
        );

        let millis = since_date.timestamp_millis();
        let datetime = UNIX_EPOCH + Duration::from_millis(millis as u64);
        let naive_date = ChronoDateTime::<Utc>::from(datetime).naive_utc();
        let date_str_start = naive_date.format("%Y-%m-%d %H:%M:%S").to_string();

        // 计算结束时间：使用截止时间或默认的30天后
        let date_str_end = if let Some(cutoff) = cutoff_date {
            let cutoff_millis = cutoff.timestamp_millis();
            let cutoff_datetime = UNIX_EPOCH + Duration::from_millis(cutoff_millis as u64);
            let cutoff_naive = ChronoDateTime::<Utc>::from(cutoff_datetime).naive_utc();
            cutoff_naive.format("%Y-%m-%d %H:%M:%S").to_string()
        } else {
            naive_date
                .checked_add_signed(chrono::Duration::days(30))
                .unwrap()
                .format("%Y-%m-%d %H:%M:%S")
                .to_string()
        };

        let sql = format!(
            r#"
            select 'HN_' || F.FACTORYNAME                      FACTORY,
                TO_CHAR(ROUND(C.JKOFF * 100, 2), 'FM90.00') FILL_FACTOR,
                C.CONTAINERNAME                             LOT_NUMBER,
                TO_CHAR(TO_NUMBER(C.JKOIPM), 'FM90.00')     MAXIMUM_CURRENT,
                TO_CHAR(TO_NUMBER(C.JKOPMAX), 'FM999.00')   MAXIMUM_POWER,
                TO_CHAR(TO_NUMBER(C.JKOVPM), 'FM90.00')     MAXIMUM_VOLTAGE,
                U.ATTRIBUTEVALUE                            MODULE_NAME,
                TO_CHAR(TO_NUMBER(C.JKOVOC), 'FM90.00')     OPEN_CIRCUIT_VOLTAGE,
                SYSDATE                                     CREATE_DATE,
                C.LASTACTIVITYDATE                          PRODUCT_DATE,
                TO_CHAR(TO_NUMBER(C.JKOISC), 'FM90.00')     SHORT_CIRCUIT_CURRENT
            from CONTAINER c
                    INNER JOIN MFGORDER MO ON C.MFGORDERID = MO.MFGORDERID
                    INNER JOIN USERATTRIBUTE U ON MO.MFGORDERID = U.PARENTID
                AND U.USERATTRIBUTENAME = 'TYPOGRAPHY'
                    INNER JOIN FACTORY F ON MO.JKOFACTORYID = F.FACTORYID
            where c.LASTACTIVITYDATE >= to_date('{}', 'yyyy-mm-dd hh24:mi:ss')
            and c.LASTACTIVITYDATE <= to_date('{}', 'yyyy-mm-dd hh24:mi:ss')
            and c.JKOFITESTDATE is not null"#,
            date_str_start, date_str_end
        );

        let conn = self.pool.get()?;

        // 使用1作为默认值
        let lot_param = last_lot_number.unwrap_or_else(|| "1".to_string());

        // info!("Executing SQL: {}", sql.replace('\n', " ").trim());
        info!(
            "Binding parameters - date_str_start: {}, date_str_end: {}, lot_param: '{}'",
            date_str_start, date_str_end, lot_param
        );

        // 添加性能监控日志
        let start = std::time::Instant::now();
        let rows = conn.query(&sql, &[])?;
        let duration = start.elapsed();

        if duration.as_secs() > 1 {
            info!("Slow query detected: {} seconds", duration.as_secs());
            // 可以在这里添加执行计划分析
            conn.query(
                "SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY_CURSOR(NULL,NULL,'ALLSTATS LAST'))",
                &[],
            )?;
        }

        let mut modules = Vec::new();
        let mut max_product_date: Option<String> = None;
        let mut count = 0;

        for row_result in rows {
            let row = row_result?;
            let product_date_str: String = row.get("PRODUCT_DATE")?;

            // 更新最大的 PRODUCT_DATE
            max_product_date = match &max_product_date {
                Some(current_max) => {
                    if product_date_str > *current_max {
                        Some(product_date_str.clone())
                    } else {
                        Some(current_max.clone())
                    }
                }
                None => Some(product_date_str.clone()),
            };

            // 解析日期字符串（格式根据实际数据库格式调整）
            let create_date: String = row.get("CREATE_DATE")?;
            let product_date: String = row.get("PRODUCT_DATE")?;
            let create_dt = NaiveDateTime::parse_from_str(&create_date, "%Y-%m-%d %H:%M:%S")?;
            let product_dt = NaiveDateTime::parse_from_str(&product_date, "%Y-%m-%d %H:%M:%S")?;

            // 转换为 MongoDB DateTime
            let create_timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(create_dt, Utc).timestamp_millis();
            let product_timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(product_dt, Utc)
                    .timestamp_millis();

            // 构建 module 对象
            let module = SolarModule {
                id: None,
                create_date: DateTime::from_millis(create_timestamp),
                factory: self.get_string(&row, "FACTORY")?,
                fill_factor: self.get_string(&row, "FILL_FACTOR")?,
                lot_number: self.get_string(&row, "LOT_NUMBER")?,
                maximum_current: self.get_string(&row, "MAXIMUM_CURRENT")?,
                maximum_power: self.get_string(&row, "MAXIMUM_POWER")?,
                maximum_voltage: self.get_string(&row, "MAXIMUM_VOLTAGE")?,
                module_name: self.get_string(&row, "MODULE_NAME")?,
                open_circuit_voltage: self.get_string(&row, "OPEN_CIRCUIT_VOLTAGE")?,
                product_date: DateTime::from_millis(product_timestamp),
                short_circuit_current: self.get_string(&row, "SHORT_CIRCUIT_CURRENT")?,
            };

            modules.push(module);
            count += 1;
        }

        // 这里的 max_product_date 就是最大的 PRODUCT_DATE
        info!(
            "Retrieved {} records. Max product date: {:?}",
            count, max_product_date
        );

        // 转换最大产品日期为 MongoDB DateTime
        let max_product_datetime = if let Some(time_str) = max_product_date {
            let dt = NaiveDateTime::parse_from_str(&time_str, "%Y-%m-%d %H:%M:%S")
                .map_err(|e| anyhow::anyhow!("Failed to parse product_date: {}", e))?;
            let timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(dt, Utc).timestamp_millis();
            Some(DateTime::from_millis(timestamp))
        } else {
            None
        };

        Ok((modules, max_product_datetime))
    }

    pub fn query_modules_by_pallet(&self, pallet_no: &str) -> Result<Vec<SolarModule>> {
        // 打印查询参数
        info!("Querying modules for pallet_no: {}", pallet_no);
        let sql = format!(
            r#"
            SELECT 'HN_' || F.FACTORYNAME                      FACTORY,
                TO_CHAR(ROUND(C.JKOFF * 100, 2), 'FM90.00') FILL_FACTOR,
                C.CONTAINERNAME                             LOT_NUMBER,
                TO_CHAR(TO_NUMBER(C.JKOIPM), 'FM90.00')     MAXIMUM_CURRENT,
                TO_CHAR(TO_NUMBER(C.JKOPMAX), 'FM999.00')   MAXIMUM_POWER,
                TO_CHAR(TO_NUMBER(C.JKOVPM), 'FM90.00')     MAXIMUM_VOLTAGE,
                U.ATTRIBUTEVALUE                            MODULE_NAME,
                TO_CHAR(TO_NUMBER(C.JKOVOC), 'FM90.00')     OPEN_CIRCUIT_VOLTAGE,
                SYSDATE                                     CREATE_DATE,
                C.LASTACTIVITYDATE                          PRODUCT_DATE,
                TO_CHAR(TO_NUMBER(C.JKOISC), 'FM90.00')     SHORT_CIRCUIT_CURRENT
            FROM CONTAINER C
                    INNER JOIN MFGORDER MO ON C.MFGORDERID = MO.MFGORDERID
                    INNER JOIN USERATTRIBUTE U ON MO.MFGORDERID = U.PARENTID
                    AND U.USERATTRIBUTENAME = 'TYPOGRAPHY'
                    INNER JOIN FACTORY F ON MO.JKOFACTORYID = F.FACTORYID
            WHERE C.PARENTCONTAINERID in (SELECT CONTAINERID FROM CONTAINER WHERE CONTAINERNAME = '{}')"#,
            pallet_no
        );

        info!("Executing SQL: {}", sql.replace('\n', " ").trim());
        info!("正在获取数据库连接...");
        let conn = self.pool.get()?;
        info!("成功获取数据库连接");

        info!("开始执行查询...");
        let rows = conn.query(&sql, &[])?;
        // 打印查询结果
        info!("Query result: {:?}", rows);

        let mut modules = Vec::new();
        let mut count = 0;

        for row_result in rows {
            let row = row_result?;

            // 解析日期字符串
            let create_date: String = row.get("CREATE_DATE")?;
            let product_date: String = row.get("PRODUCT_DATE")?;
            let create_dt = NaiveDateTime::parse_from_str(&create_date, "%Y-%m-%d %H:%M:%S")?;
            let product_dt = NaiveDateTime::parse_from_str(&product_date, "%Y-%m-%d %H:%M:%S")?;

            // 转换为 MongoDB DateTime
            let create_timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(create_dt, Utc).timestamp_millis();
            let product_timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(product_dt, Utc)
                    .timestamp_millis();

            // 构建 module 对象
            let module = SolarModule {
                id: None,
                create_date: DateTime::from_millis(create_timestamp),
                factory: self.get_string(&row, "FACTORY")?,
                fill_factor: self.get_string(&row, "FILL_FACTOR")?,
                lot_number: self.get_string(&row, "LOT_NUMBER")?,
                maximum_current: self.get_string(&row, "MAXIMUM_CURRENT")?,
                maximum_power: self.get_string(&row, "MAXIMUM_POWER")?,
                maximum_voltage: self.get_string(&row, "MAXIMUM_VOLTAGE")?,
                module_name: self.get_string(&row, "MODULE_NAME")?,
                open_circuit_voltage: self.get_string(&row, "OPEN_CIRCUIT_VOLTAGE")?,
                product_date: DateTime::from_millis(product_timestamp),
                short_circuit_current: self.get_string(&row, "SHORT_CIRCUIT_CURRENT")?,
            };

            modules.push(module);
            count += 1;
        }

        info!("Retrieved {} records for pallet_no: {}", count, pallet_no);
        Ok(modules)
    }

    pub fn query_modules_by_lot_number(&self, lot_number: &str) -> Result<Vec<SolarModule>> {
        // 打印查询参数
        info!("Querying modules for lot_number: {}", lot_number);
        let sql = format!(
            r#"
            SELECT 'HN_' || F.FACTORYNAME                      FACTORY,
                TO_CHAR(ROUND(C.JKOFF * 100, 2), 'FM90.00') FILL_FACTOR,
                C.CONTAINERNAME                             LOT_NUMBER,
                TO_CHAR(TO_NUMBER(C.JKOIPM), 'FM90.00')     MAXIMUM_CURRENT,
                TO_CHAR(TO_NUMBER(C.JKOPMAX), 'FM999.00')   MAXIMUM_POWER,
                TO_CHAR(TO_NUMBER(C.JKOVPM), 'FM90.00')     MAXIMUM_VOLTAGE,
                U.ATTRIBUTEVALUE                            MODULE_NAME,
                TO_CHAR(TO_NUMBER(C.JKOVOC), 'FM90.00')     OPEN_CIRCUIT_VOLTAGE,
                SYSDATE                                     CREATE_DATE,
                C.LASTACTIVITYDATE                          PRODUCT_DATE,
                TO_CHAR(TO_NUMBER(C.JKOISC), 'FM90.00')     SHORT_CIRCUIT_CURRENT
            FROM CONTAINER C
                    INNER JOIN MFGORDER MO ON C.MFGORDERID = MO.MFGORDERID
                    INNER JOIN USERATTRIBUTE U ON MO.MFGORDERID = U.PARENTID
                    AND U.USERATTRIBUTENAME = 'TYPOGRAPHY'
                    INNER JOIN FACTORY F ON MO.JKOFACTORYID = F.FACTORYID
            WHERE C.CONTAINERNAME = '{}'"#,
            lot_number
        );

        info!("Executing SQL: {}", sql.replace('\n', " ").trim());
        info!("正在获取数据库连接...");
        let conn = self.pool.get()?;
        info!("成功获取数据库连接");

        info!("开始执行查询...");
        let rows = conn.query(&sql, &[])?;
        // 打印查询结果
        info!("Query result: {:?}", rows);

        let mut modules = Vec::new();
        let mut count = 0;

        for row_result in rows {
            let row = row_result?;

            // 解析日期字符串
            let create_date: String = row.get("CREATE_DATE")?;
            let product_date: String = row.get("PRODUCT_DATE")?;
            let create_dt = NaiveDateTime::parse_from_str(&create_date, "%Y-%m-%d %H:%M:%S")?;
            let product_dt = NaiveDateTime::parse_from_str(&product_date, "%Y-%m-%d %H:%M:%S")?;

            // 转换为 MongoDB DateTime
            let create_timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(create_dt, Utc).timestamp_millis();
            let product_timestamp =
                ChronoDateTime::<Utc>::from_naive_utc_and_offset(product_dt, Utc)
                    .timestamp_millis();

            // 构建 module 对象
            let module = SolarModule {
                id: None,
                create_date: DateTime::from_millis(create_timestamp),
                factory: self.get_string(&row, "FACTORY")?,
                fill_factor: self.get_string(&row, "FILL_FACTOR")?,
                lot_number: self.get_string(&row, "LOT_NUMBER")?,
                maximum_current: self.get_string(&row, "MAXIMUM_CURRENT")?,
                maximum_power: self.get_string(&row, "MAXIMUM_POWER")?,
                maximum_voltage: self.get_string(&row, "MAXIMUM_VOLTAGE")?,
                module_name: self.get_string(&row, "MODULE_NAME")?,
                open_circuit_voltage: self.get_string(&row, "OPEN_CIRCUIT_VOLTAGE")?,
                product_date: DateTime::from_millis(product_timestamp),
                short_circuit_current: self.get_string(&row, "SHORT_CIRCUIT_CURRENT")?,
            };

            modules.push(module);
            count += 1;
        }

        info!("Retrieved {} records for lot_number: {}", count, lot_number);
        Ok(modules)
    }

    // 辅助方法：安全地获取字符串值，处理 NULL 值
    fn get_string(&self, row: &r2d2_oracle::oracle::Row, column: &str) -> Result<String> {
        Ok(row.get::<_, Option<String>>(column)?.unwrap_or_default())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::OracleConfig;
    use env_logger;

    #[test]
    fn test_oracle_connection() -> Result<()> {
        // 初始化日志系统
        init_logger()?;

        // 创建测试配置
        info!("开始创建测试配置...");
        let config = OracleConfig {
            username: std::env::var("ORACLE_USERNAME").unwrap_or_else(|_| "HNZJMESDB".to_string()),
            password: std::env::var("ORACLE_PASSWORD")
                .unwrap_or_else(|_| "Sie$2023Camstar".to_string()),
            host: std::env::var("ORACLE_HOST").unwrap_or_else(|_| "*************".to_string()),
            port: std::env::var("ORACLE_PORT")
                .unwrap_or_else(|_| "1521".to_string())
                .parse()
                .unwrap(),
            database: std::env::var("ORACLE_DATABASE").unwrap_or_else(|_| "HNZJMESDG".to_string()),
        };
        info!(
            "测试配置创建完成: {}:{}/{}",
            config.host, config.port, config.database
        );

        info!("正在创建 Oracle 客户端...");
        let client = OracleClient::new(&config)?;
        info!("Oracle 客户端创建成功");

        let mut pallet_no = "6100001505-3-0079";

        let sql = format!(
            r#"SELECT * FROM CONTAINER WHERE CONTAINERNAME = '{}'"#,
            pallet_no
        );
        info!("准备执行测试查询: {}", sql);

        info!("正在获取数据库连接...");
        let conn = client.pool.get()?;
        info!("成功获取数据库连接");

        info!("开始执行查询...");
        let rows = conn.query(&sql, &[])?;

        let count = rows.count();
        info!("共查询到 {} 条记录", count);

        pallet_no = "6100001505-3-0080";
        let sql = r#"SELECT * FROM CONTAINER WHERE CONTAINERNAME = :1"#;
        info!("准备执行测试查询: {}", sql);

        info!("正在获取数据库连接...");
        let conn = client.pool.get()?;
        info!("成功获取数据库连接");

        info!("开始执行查询...");
        let rows = conn.query(&sql, &[&pallet_no])?;

        let count = rows.count();
        info!("共查询到 {} 条记录", count);
        info!("测试成功完成");
        Ok(())
    }
}
