@echo off
echo ===== Product Data Sync NSSM Deployment =====

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Configuration
set SERVICE_NAME=ProductDataSync
set APP_DIR=D:\0.MES\0.ANewCode\product-data-sync
set ORACLE_DIR=D:\0.MES\0.ANewCode\instantclient_23_6
set NSSM_PATH=%APP_DIR%\nssm.exe

echo 1. Checking NSSM...
if not exist "%NSSM_PATH%" (
    echo NSSM not found. Downloading...
    echo Please download NSSM from https://nssm.cc/download
    echo Extract nssm.exe to: %APP_DIR%
    echo Then run this script again.
    pause
    exit /b 1
)

echo 2. Building release version...
cd /d "%APP_DIR%"
cargo build --release
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo 3. Setting system environment variables...
setx OCI_LIB_DIR "%ORACLE_DIR%" /M
setx ORACLE_HOME "%ORACLE_DIR%" /M

REM Get current system PATH and add Oracle if not present
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set SYSTEM_PATH=%%b
echo %SYSTEM_PATH% | findstr /C:"%ORACLE_DIR%" >nul
if %errorLevel% neq 0 (
    setx PATH "%ORACLE_DIR%;%SYSTEM_PATH%" /M
)

echo 4. Removing existing service if present...
"%NSSM_PATH%" stop "%SERVICE_NAME%" >nul 2>&1
"%NSSM_PATH%" remove "%SERVICE_NAME%" confirm >nul 2>&1

echo 5. Installing service with NSSM...
"%NSSM_PATH%" install "%SERVICE_NAME%" "%APP_DIR%\target\release\product-data-sync.exe"

echo 6. Configuring service...
"%NSSM_PATH%" set "%SERVICE_NAME%" DisplayName "Product Data Sync Service"
"%NSSM_PATH%" set "%SERVICE_NAME%" Description "Product Data Sync Service for Oracle and MongoDB integration"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppDirectory "%APP_DIR%"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppEnvironmentExtra "OCI_LIB_DIR=%ORACLE_DIR%" "ORACLE_HOME=%ORACLE_DIR%" "PATH=%ORACLE_DIR%;%SYSTEM_PATH%"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppStdout "%APP_DIR%\logs\service_stdout.log"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppStderr "%APP_DIR%\logs\service_stderr.log"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateFiles 1
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateOnline 1
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateSeconds 86400
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRotateBytes 10485760

echo 7. Starting service...
"%NSSM_PATH%" start "%SERVICE_NAME%"

echo 8. Checking service status...
timeout /t 5 /nobreak >nul
sc query "%SERVICE_NAME%"

echo.
echo ===== NSSM Deployment Complete =====
echo Service Name: %SERVICE_NAME%
echo Management: Use NSSM commands or Windows Services
echo Logs: %APP_DIR%\logs\
echo.
echo NSSM Commands:
echo   Start:   nssm start %SERVICE_NAME%
echo   Stop:    nssm stop %SERVICE_NAME%
echo   Restart: nssm restart %SERVICE_NAME%
echo   Status:  nssm status %SERVICE_NAME%
echo   Remove:  nssm remove %SERVICE_NAME%
echo.

pause
