# Oracle客户端安装指南

## 问题诊断

当前错误：
```
DPI Error: DPI-1047: Cannot locate a 64-bit Oracle Client library: "The specified module could not be found"
```

这表明系统缺少Oracle客户端库，需要安装Oracle Instant Client。

## 解决方案

### 方案1：安装Oracle Instant Client（推荐）

#### 1. 下载Oracle Instant Client
访问Oracle官网下载页面：
https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html

下载以下文件：
- **Basic Package** (instantclient-basic-windows.x64-*********.0dbru.zip)
- **SQL*Plus Package** (可选，用于测试连接)

#### 2. 安装步骤
```bash
# 1. 创建目录
mkdir C:\oracle\instantclient_21_13

# 2. 解压下载的文件到该目录
# 解压后目录结构应该是：
# C:\oracle\instantclient_21_13\
#   ├── oci.dll
#   ├── oraociei21.dll
#   ├── oraocci21.dll
#   └── 其他dll文件...
```

#### 3. 配置环境变量
在Windows系统中添加环境变量：

**方法A：通过系统设置**
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 在"系统变量"中找到"Path"，点击"编辑"
4. 添加：`C:\oracle\instantclient_21_13`
5. 点击"确定"保存

**方法B：通过PowerShell（管理员权限）**
```powershell
# 添加到系统PATH
[Environment]::SetEnvironmentVariable("Path", $env:Path + ";C:\oracle\instantclient_21_13", [EnvironmentVariableTarget]::Machine)

# 设置Oracle相关环境变量（可选）
[Environment]::SetEnvironmentVariable("ORACLE_HOME", "C:\oracle\instantclient_21_13", [EnvironmentVariableTarget]::Machine)
```

#### 4. 验证安装
重新打开PowerShell或命令提示符，运行：
```bash
# 检查PATH是否包含Oracle目录
echo $env:PATH

# 检查Oracle库文件是否存在
dir C:\oracle\instantclient_21_13\oci.dll
```

### 方案2：使用Docker（推荐用于开发环境）

如果不想在本地安装Oracle客户端，可以使用Docker：

#### 1. 创建Dockerfile
```dockerfile
FROM rust:1.87

# 安装Oracle Instant Client
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    libaio1 \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Oracle Instant Client
RUN wget https://download.oracle.com/otn_software/linux/instantclient/2113000/instantclient-basic-linux.x64-*********.0dbru.zip \
    && unzip instantclient-basic-linux.x64-*********.0dbru.zip \
    && mv instantclient_21_13 /opt/oracle \
    && rm instantclient-basic-linux.x64-*********.0dbru.zip

# 设置环境变量
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_21_13:$LD_LIBRARY_PATH
ENV PATH=/opt/oracle/instantclient_21_13:$PATH

WORKDIR /app
COPY . .
RUN cargo build --release

EXPOSE 8080
CMD ["./target/release/product-data-sync"]
```

#### 2. 构建和运行
```bash
docker build -t product-data-sync .
docker run -p 8080:8080 product-data-sync
```

### 方案3：使用预编译的Oracle客户端

#### 1. 下载预编译版本
```bash
# 使用chocolatey安装（如果已安装chocolatey）
choco install oracle-instantclient

# 或者使用winget
winget install Oracle.InstantClient
```

## 连接测试

### 1. 使用SQL*Plus测试（如果安装了）
```bash
sqlplus HNZJMESDB/Sie$2023Camstar@*************:1521/HNZJMESDG
```

### 2. 使用程序测试
重新启动程序：
```bash
cargo run
```

如果看到以下日志而不是DPI错误，说明Oracle客户端安装成功：
```
[INFO] Starting application...
[INFO] Configuration loaded successfully
[INFO] Database connections established
```

## 故障排除

### 问题1：仍然报DPI-1047错误
**解决方案：**
1. 确认PATH环境变量包含Oracle Instant Client目录
2. 重启PowerShell/命令提示符
3. 重启IDE或编辑器
4. 检查是否下载了64位版本

### 问题2：找不到特定的DLL文件
**解决方案：**
1. 确认下载了完整的Basic Package
2. 检查解压是否完整
3. 可能需要安装Visual C++ Redistributable

### 问题3：连接超时或网络错误
**解决方案：**
1. 检查网络连接到Oracle服务器
2. 确认防火墙设置
3. 验证Oracle服务器地址和端口

### 问题4：认证失败
**解决方案：**
1. 检查用户名和密码
2. 确认服务名（HNZJMESDG）是否正确
3. 联系数据库管理员确认权限

## 配置验证

安装完成后，可以通过以下方式验证配置：

### 1. 检查环境变量
```powershell
echo $env:PATH | Select-String "oracle"
```

### 2. 检查DLL文件
```powershell
Get-ChildItem "C:\oracle\instantclient_21_13\*.dll"
```

### 3. 运行程序
```bash
cargo run
```

成功的话应该看到：
```
[INFO] Starting application...
[INFO] Configuration loaded successfully  
[INFO] Database connections established
[INFO] Starting HTTP server at http://127.0.0.1:8080
```

## 生产环境部署

在生产环境中，建议：
1. 使用Docker容器化部署
2. 或者在服务器上安装完整的Oracle Client
3. 配置连接池参数以优化性能
4. 设置适当的超时和重试机制
