# 修复Oracle路径的脚本

Write-Host "=== 修复Oracle环境变量 ===" -ForegroundColor Green

# 设置Oracle路径
$oraclePath = "D:\0.MES\0.ANewCode\instantclient_23_6"

# 检查Oracle目录是否存在
if (!(Test-Path $oraclePath)) {
    Write-Host "错误: Oracle路径不存在: $oraclePath" -ForegroundColor Red
    exit 1
}

# 检查关键DLL文件
$requiredDlls = @("oci.dll", "oraociei.dll", "oraocci23.dll")
foreach ($dll in $requiredDlls) {
    $dllPath = Join-Path $oraclePath $dll
    if (!(Test-Path $dllPath)) {
        Write-Host "错误: 缺少必要的DLL文件: $dll" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✓ Oracle客户端文件检查通过" -ForegroundColor Green

# 设置当前会话的环境变量
Write-Host "设置当前会话的PATH环境变量..." -ForegroundColor Yellow

# 获取系统和用户PATH
$systemPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::Machine)
$userPath = [Environment]::GetEnvironmentVariable("Path", [EnvironmentVariableTarget]::User)

# 合并PATH并确保Oracle路径在前面
$newPath = "$oraclePath;$systemPath;$userPath"

# 设置当前会话的PATH
$env:PATH = $newPath

Write-Host "✓ PATH环境变量已更新" -ForegroundColor Green

# 验证Oracle DLL是否可以找到
Write-Host "验证Oracle DLL..." -ForegroundColor Yellow

try {
    # 尝试加载Oracle DLL
    $oracleDll = Join-Path $oraclePath "oci.dll"
    if (Test-Path $oracleDll) {
        Write-Host "✓ 可以找到 oci.dll" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ 无法验证Oracle DLL" -ForegroundColor Red
}

# 设置额外的Oracle环境变量
$env:ORACLE_HOME = $oraclePath
Write-Host "✓ ORACLE_HOME已设置为: $oraclePath" -ForegroundColor Green

# 显示当前PATH中的Oracle路径
Write-Host "当前PATH中的Oracle相关路径:" -ForegroundColor Cyan
$env:PATH -split ';' | Where-Object { $_ -like "*oracle*" -or $_ -like "*instantclient*" } | ForEach-Object {
    Write-Host "  - $_" -ForegroundColor White
}

Write-Host ""
Write-Host "=== 环境变量修复完成 ===" -ForegroundColor Green
Write-Host "现在可以运行程序了:" -ForegroundColor Yellow
Write-Host "  cargo run" -ForegroundColor White
