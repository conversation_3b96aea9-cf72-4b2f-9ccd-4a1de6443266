use std::path::Path;
use serde::Deserialize;
use anyhow::Result;

#[derive(Debug, Deserialize)]
pub struct SyncConfig {
    pub interval_seconds: u64,
    pub batch_size: i32,
    pub data_delay_days: Option<u64>, // 数据延迟天数，只同步N天前的数据，默认7天
}

#[derive(Debug, Deserialize)]
pub struct LogConfig {
    pub level: Option<String>,
    pub max_file_size_mb: Option<u64>,
    pub max_files: Option<u32>,
    pub log_dir: Option<String>,
    pub enable_console: Option<bool>,
    pub cleanup_days: Option<u64>,
}

#[derive(Debug, Deserialize)]
pub struct Config {
    pub oracle: OracleConfig,
    pub mongodb: MongoConfig,
    pub sync: SyncConfig,  // 添加同步配置
    pub logging: Option<LogConfig>,  // 添加日志配置
}

#[derive(Debug, Deserialize)]
pub struct OracleConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub database: String,  // 这里实际上是服务名
}

#[derive(Debug, Deserialize)]
pub struct MongoConfig {
    pub uri: String,
    pub database: String,
}

impl Config {
    pub fn load() -> Result<Self> {
        let config_path = if Path::new("config/default.toml").exists() {
            "config/default.toml"
        } else {
            "config/default"
        };

        let settings = ::config::Config::builder()
            .add_source(::config::File::with_name(config_path))
            .add_source(::config::Environment::with_prefix("APP"))
            .build()?;

        Ok(settings.try_deserialize()?)
    }
}