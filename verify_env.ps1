# 验证环境变量脚本
Write-Host "=== 验证Oracle环境变量 ===" -ForegroundColor Green

Write-Host "1. 检查OCI_LIB_DIR:" -ForegroundColor Yellow
if ($env:OCI_LIB_DIR) {
    Write-Host "  ✓ OCI_LIB_DIR = $env:OCI_LIB_DIR" -ForegroundColor Green
} else {
    Write-Host "  ✗ OCI_LIB_DIR 未设置" -ForegroundColor Red
}

Write-Host "2. 检查PATH中的Oracle路径:" -ForegroundColor Yellow
$oraclePaths = $env:PATH -split ';' | Where-Object { $_ -like "*oracle*" -or $_ -like "*instantclient*" }
if ($oraclePaths) {
    foreach ($path in $oraclePaths) {
        Write-Host "  ✓ $path" -ForegroundColor Green
    }
} else {
    Write-Host "  ✗ PATH中未找到Oracle路径" -ForegroundColor Red
}

Write-Host "3. 检查Oracle DLL文件:" -ForegroundColor Yellow
$oracleDir = "D:\0.MES\0.ANewCode\instantclient_23_6"
$requiredDlls = @("oci.dll", "oraociei.dll", "oraocci23.dll")

foreach ($dll in $requiredDlls) {
    $dllPath = Join-Path $oracleDir $dll
    if (Test-Path $dllPath) {
        Write-Host "  ✓ $dll" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $dll (缺失)" -ForegroundColor Red
    }
}

Write-Host "4. 测试where命令:" -ForegroundColor Yellow
try {
    $ociLocation = where.exe oci.dll 2>$null
    if ($ociLocation) {
        Write-Host "  ✓ 可以找到 oci.dll: $ociLocation" -ForegroundColor Green
    } else {
        Write-Host "  ✗ 无法找到 oci.dll" -ForegroundColor Red
    }
} catch {
    Write-Host "  ✗ where命令执行失败" -ForegroundColor Red
}

Write-Host ""
if ($env:OCI_LIB_DIR -and ($env:PATH -like "*instantclient*")) {
    Write-Host "✓ 环境变量配置正确，可以运行程序" -ForegroundColor Green
    Write-Host "运行: cargo run" -ForegroundColor White
} else {
    Write-Host "✗ 环境变量配置有问题，需要重启PowerShell" -ForegroundColor Red
    Write-Host "请关闭PowerShell并重新打开" -ForegroundColor Yellow
}
