# Oracle连接测试脚本

Write-Host "=== Oracle连接诊断工具 ===" -ForegroundColor Green

# 1. 检查环境变量
Write-Host "`n1. 检查环境变量..." -ForegroundColor Yellow
$path = $env:PATH
$oracleInPath = $path -split ';' | Where-Object { $_ -like "*oracle*" -or $_ -like "*instantclient*" }

if ($oracleInPath) {
    Write-Host "✓ 在PATH中找到Oracle路径:" -ForegroundColor Green
    foreach ($p in $oracleInPath) {
        Write-Host "  - $p" -ForegroundColor White
    }
} else {
    Write-Host "✗ PATH中未找到Oracle路径" -ForegroundColor Red
}

if ($env:ORACLE_HOME) {
    Write-Host "✓ ORACLE_HOME: $env:ORACLE_HOME" -ForegroundColor Green
} else {
    Write-Host "! ORACLE_HOME未设置（可选）" -ForegroundColor Yellow
}

# 2. 检查Oracle客户端文件
Write-Host "`n2. 检查Oracle客户端文件..." -ForegroundColor Yellow
$commonPaths = @(
    "C:\oracle\instantclient_21_13",
    "C:\oracle\instantclient_19_3",
    "C:\oracle\instantclient",
    "C:\app\oracle\product\*\client_*",
    "C:\Program Files\Oracle\*"
)

$foundPath = $null
$requiredFiles = @("oci.dll", "oraociei21.dll", "oraocci21.dll")

foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        Write-Host "检查路径: $path" -ForegroundColor White
        $allFilesExist = $true
        foreach ($file in $requiredFiles) {
            $filePath = Join-Path $path $file
            if (Test-Path $filePath) {
                Write-Host "  ✓ $file" -ForegroundColor Green
            } else {
                Write-Host "  ✗ $file (缺失)" -ForegroundColor Red
                $allFilesExist = $false
            }
        }
        if ($allFilesExist) {
            $foundPath = $path
            break
        }
    }
}

if (!$foundPath) {
    Write-Host "✗ 未找到完整的Oracle客户端安装" -ForegroundColor Red
}

# 3. 检查网络连接
Write-Host "`n3. 检查网络连接..." -ForegroundColor Yellow
$oracleHost = "*************"
$oraclePort = 1521

try {
    $connection = Test-NetConnection -ComputerName $oracleHost -Port $oraclePort -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✓ 可以连接到Oracle服务器 ${oracleHost}:${oraclePort}" -ForegroundColor Green
    } else {
        Write-Host "✗ 无法连接到Oracle服务器 ${oracleHost}:${oraclePort}" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 网络连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 检查配置文件
Write-Host "`n4. 检查配置文件..." -ForegroundColor Yellow
$configFile = "config\default.toml"
if (Test-Path $configFile) {
    Write-Host "✓ 配置文件存在: $configFile" -ForegroundColor Green
    try {
        $config = Get-Content $configFile -Raw
        if ($config -match 'host\s*=\s*"([^"]+)"') {
            Write-Host "  Oracle主机: $($matches[1])" -ForegroundColor White
        }
        if ($config -match 'port\s*=\s*(\d+)') {
            Write-Host "  Oracle端口: $($matches[1])" -ForegroundColor White
        }
        if ($config -match 'database\s*=\s*"([^"]+)"') {
            Write-Host "  服务名: $($matches[1])" -ForegroundColor White
        }
    } catch {
        Write-Host "  ! 无法解析配置文件" -ForegroundColor Yellow
    }
} else {
    Write-Host "✗ 配置文件不存在: $configFile" -ForegroundColor Red
}

# 5. 生成诊断报告
Write-Host "`n=== 诊断结果 ===" -ForegroundColor Cyan

if (!$foundPath) {
    Write-Host "主要问题: Oracle客户端未安装或配置不正确" -ForegroundColor Red
    Write-Host ""
    Write-Host "解决步骤:" -ForegroundColor Yellow
    Write-Host "1. 运行安装脚本: .\install_oracle_client.ps1" -ForegroundColor White
    Write-Host "2. 或手动下载Oracle Instant Client:" -ForegroundColor White
    Write-Host "   https://www.oracle.com/database/technologies/instant-client/winx64-64-downloads.html" -ForegroundColor White
    Write-Host "3. 解压到 C:\oracle\instantclient_21_13" -ForegroundColor White
    Write-Host "4. 添加到系统PATH环境变量" -ForegroundColor White
    Write-Host "5. 重启PowerShell并重新运行程序" -ForegroundColor White
} else {
    Write-Host "✓ Oracle客户端已正确安装在: $foundPath" -ForegroundColor Green

    if (!$oracleInPath) {
        Write-Host "! 需要将Oracle路径添加到PATH环境变量" -ForegroundColor Yellow
        Write-Host "运行以下命令（需要管理员权限）:" -ForegroundColor White
        Write-Host "[Environment]::SetEnvironmentVariable('Path', `$env:Path + ';${foundPath}', [EnvironmentVariableTarget]::Machine)" -ForegroundColor Gray
    }
}

# 6. 提供下一步建议
Write-Host "`n=== 下一步操作 ===" -ForegroundColor Cyan

if ($foundPath -and $oracleInPath) {
    Write-Host "Oracle客户端配置正确，可以尝试运行程序:" -ForegroundColor Green
    Write-Host "  cargo run" -ForegroundColor White
    Write-Host ""
    Write-Host "如果仍然出现DPI-1047错误，请:" -ForegroundColor Yellow
    Write-Host "1. 重启PowerShell" -ForegroundColor White
    Write-Host "2. 检查是否需要Visual C++ Redistributable" -ForegroundColor White
    Write-Host "3. 确认下载的是64位版本的Oracle客户端" -ForegroundColor White
} else {
    Write-Host "请先解决Oracle客户端安装问题，然后重新运行此诊断脚本" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "详细安装说明请查看: ORACLE_CLIENT_SETUP.md" -ForegroundColor Cyan
