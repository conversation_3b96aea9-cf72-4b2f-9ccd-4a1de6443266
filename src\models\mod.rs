use serde::{Serialize, Deserialize};
use mongodb::bson::{self, DateTime};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SolarModule {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<bson::oid::ObjectId>,
    #[serde(rename = "CreateDate")]
    pub create_date: DateTime,
    #[serde(rename = "Factory")]
    pub factory: String,
    #[serde(rename = "FillFactor")]
    pub fill_factor: String,
    #[serde(rename = "LotNumber")]
    pub lot_number: String,
    #[serde(rename = "MaximumCurrent")]
    pub maximum_current: String,
    #[serde(rename = "MaximumPower")]
    pub maximum_power: String,
    #[serde(rename = "MaximumVoltage")]
    pub maximum_voltage: String,
    #[serde(rename = "ModuleName")]
    pub module_name: String,
    #[serde(rename = "OpenCircuitVoltage")]
    pub open_circuit_voltage: String,
    #[serde(rename = "ProductDate")]
    pub product_date: DateTime,
    #[serde(rename = "ShortCircuitCurrent")]
    pub short_circuit_current: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SyncState {
    pub last_sync_time: DateTime,
    pub last_lot_number: Option<String>,
}