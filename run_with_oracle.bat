@echo off
chcp 65001 >nul
echo ===== Oracle Environment Setup and Program Start =====

REM Set Oracle environment variables
set OCI_LIB_DIR=D:\0.MES\0.ANewCode\instantclient_23_6
set ORACLE_HOME=D:\0.MES\0.ANewCode\instantclient_23_6
set PATH=D:\0.MES\0.ANewCode\instantclient_23_6;%PATH%

echo 1. Setting Oracle environment variables...
echo    OCI_LIB_DIR=%OCI_LIB_DIR%
echo    ORACLE_HOME=%ORACLE_HOME%
echo    Oracle path added to PATH

REM Verify Oracle files exist
echo.
echo 2. Verifying Oracle client files...
if exist "%OCI_LIB_DIR%\oci.dll" (
    echo    [OK] oci.dll exists
) else (
    echo    [ERROR] oci.dll not found
    goto :error
)

if exist "%OCI_LIB_DIR%\oraociei.dll" (
    echo    [OK] oraociei.dll exists
) else (
    echo    [ERROR] oraociei.dll not found
    goto :error
)

if exist "%OCI_LIB_DIR%\oraocci23.dll" (
    echo    [OK] oraocci23.dll exists
) else (
    echo    [ERROR] oraocci23.dll not found
    goto :error
)

REM Change to project directory
echo.
echo 3. Changing to project directory...
cd /d "D:\0.MES\0.ANewCode\product-data-sync"
if errorlevel 1 (
    echo    [ERROR] Cannot change to project directory
    goto :error
)
echo    [OK] Current directory: %CD%

REM Show environment info
echo.
echo 4. Environment information:
echo    Rust version:
rustc --version
echo    Cargo version:
cargo --version

REM Run the program
echo.
echo 5. Starting program...
echo =====================================
cargo run

REM Handle program completion
echo.
echo =====================================
if errorlevel 1 (
    echo Program encountered an error
    goto :error
) else (
    echo Program completed successfully
)

goto :end

:error
echo.
echo ===== ERROR OCCURRED =====
echo Please check the following:
echo 1. Oracle Instant Client is correctly installed at D:\0.MES\0.ANewCode\instantclient_23_6
echo 2. Required DLL files exist
echo 3. Network connection is working
echo 4. Database configuration is correct
echo.

:end
echo.
echo Press any key to exit...
pause >nul
