# Windows Docker deployment for Product Data Sync
# Use Windows Server Core as base image
FROM mcr.microsoft.com/windows/servercore:ltsc2022

# Set working directory
WORKDIR C:\app

# Install Rust (if building in container)
# For production, it's better to build locally and copy the binary

# Copy Oracle Instant Client
COPY instantclient_23_6 C:\oracle\instantclient_23_6

# Copy application files
COPY target\release\product-data-sync.exe C:\app\
COPY config C:\app\config

# Set environment variables
ENV OCI_LIB_DIR=C:\oracle\instantclient_23_6
ENV ORACLE_HOME=C:\oracle\instantclient_23_6
ENV PATH="C:\oracle\instantclient_23_6;${PATH}"

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD powershell -command "try { Invoke-WebRequest -Uri http://localhost:8080/api/admin/status -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }"

# Run the application
CMD ["C:\\app\\product-data-sync.exe"]
