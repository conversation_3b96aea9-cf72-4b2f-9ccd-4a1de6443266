@echo off
echo ===== Product Data Sync Task Scheduler Deployment =====

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    pause
    exit /b 1
)

REM Configuration
set TASK_NAME=ProductDataSync
set APP_DIR=D:\0.MES\0.ANewCode\product-data-sync
set ORACLE_DIR=D:\0.MES\0.ANewCode\instantclient_23_6

echo 1. Building release version...
cd /d "%APP_DIR%"
cargo build --release
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo 2. Setting system environment variables...
setx OCI_LIB_DIR "%ORACLE_DIR%" /M
setx ORACLE_HOME "%ORACLE_DIR%" /M

echo 3. Creating startup script...
(
echo @echo off
echo set OCI_LIB_DIR=%ORACLE_DIR%
echo set ORACLE_HOME=%ORACLE_DIR%
echo set PATH=%ORACLE_DIR%;%%PATH%%
echo cd /d "%APP_DIR%"
echo :restart
echo "%APP_DIR%\target\release\product-data-sync.exe"
echo echo Program exited, restarting in 10 seconds...
echo timeout /t 10 /nobreak
echo goto restart
) > "%APP_DIR%\server_start.bat"

echo 4. Removing existing task if present...
schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1

echo 5. Creating scheduled task...
schtasks /create ^
    /tn "%TASK_NAME%" ^
    /tr "\"%APP_DIR%\server_start.bat\"" ^
    /sc onstart ^
    /ru "SYSTEM" ^
    /rl highest ^
    /f

if %errorLevel% neq 0 (
    echo ERROR: Failed to create scheduled task
    pause
    exit /b 1
)

echo 6. Starting task...
schtasks /run /tn "%TASK_NAME%"

echo 7. Checking task status...
timeout /t 5 /nobreak >nul
schtasks /query /tn "%TASK_NAME%"

echo.
echo ===== Task Scheduler Deployment Complete =====
echo Task Name: %TASK_NAME%
echo Startup Script: %APP_DIR%\server_start.bat
echo.
echo Task Management:
echo   Start:  schtasks /run /tn %TASK_NAME%
echo   Stop:   taskkill /f /im product-data-sync.exe
echo   Status: schtasks /query /tn %TASK_NAME%
echo   Remove: schtasks /delete /tn %TASK_NAME% /f
echo.

pause
