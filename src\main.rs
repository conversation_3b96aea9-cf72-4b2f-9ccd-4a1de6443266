// src/main.rs
use anyhow::Result;
use log::{info, error};
use tokio;
use actix_web::{App, HttpServer, web};

mod config;
mod db;
mod models;
mod tasks;
mod routes;
mod logging;

use config::Config;
use db::{MongoClient, OracleClient};
use tasks::SyncTask;

#[tokio::main]
async fn main() -> Result<()> {
    // 加载配置
    let config = Config::load()?;

    // 初始化简单的日志系统
    env_logger::init();

    info!("Starting application...");
    info!("Configuration loaded successfully");

    // 清理旧日志文件
    if let Some(log_cfg) = &config.logging {
        let cleanup_days = log_cfg.cleanup_days.unwrap_or(30);
        let log_dir = log_cfg.log_dir.as_deref().unwrap_or("logs");
        if let Err(e) = logging::cleanup_old_logs(log_dir, cleanup_days) {
            log::warn!("清理旧日志文件失败: {}", e);
        }
    }

    // 初始化数据库连接
    let oracle = OracleClient::new(&config.oracle)?;
    let mongo = MongoClient::new(&config.mongodb).await?;
    let mongo_web = mongo.clone();
    info!("Database connections established");

    // 创建同步任务
    let sync_task = SyncTask::new(
        oracle.clone(),
        mongo,
        config.sync.interval_seconds,
        config.sync.batch_size,
        config.sync.data_delay_days.unwrap_or(7), // 默认7天
    );

    // 启动同步任务
    tokio::spawn(async move {
        if let Err(e) = sync_task.start().await {
            error!("Sync task error: {}", e);
        }
    });

    // 启动 HTTP 服务器
    info!("Starting HTTP server at http://127.0.0.1:8080");
    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(oracle.clone()))
            .app_data(web::Data::new(mongo_web.clone()))
            .service(routes::module::query_modules_by_pallet)
            .service(routes::module::query_modules_by_lot_number)
            .service(routes::admin::get_log_stats)
            .service(routes::admin::cleanup_logs)
            .service(routes::admin::get_system_status)
    })
    .bind("0.0.0.0:8080")?
    .run()
    .await?;

    Ok(())
}
